@page "/training"
@attribute [Authorize(Roles = "Admin,User")]
@using AgriEnergyPlatform.Models
@using AgriEnergyPlatform.Services
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject ITrainingService TrainingService
@inject IEducationalContentService ContentService
@inject IVenueService VenueService
@inject AuthenticationStateProvider AuthProvider
@inject NavigationManager Navigation

<div class="container my-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Upcoming Training Events</h2>
        <button class="btn btn-success" @onclick="ToggleShowMyTrainings">
            @(showMyTrainings ? "Show All" : "Show My Trainings")
        </button>
        <button class="btn btn-success" @onclick="HandleMyTrainingsClick">
            My Trainings
        </button>
        @if (canCreateTraining)
        {
            <button class="btn btn-success" @onclick="ShowCreateTrainingModal">
                <i class="fas fa-plus me-2"></i>Create Training
            </button>
        }
    </div>
    @if (showCreateTrainingModal)
    {
        <div class="modal fade show d-block" tabindex="-1" style="background:rgba(0,0,0,0.3);">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Create Training Event</h5>
                        <button type="button" class="btn-close" @onclick="CloseCreateTrainingModal"></button>
                    </div>
                    <div class="modal-body">
                        <EditForm Model="newTraining" OnValidSubmit="HandleCreateTraining">
                            <DataAnnotationsValidator />
                            <ValidationSummary />
                            <div class="mb-2">
                                <label>Title</label>
                                <InputText class="form-control" @bind-Value="newTraining.Title" />
                            </div>
                            <div class="mb-2">
                                <label>Description</label>
                                <InputTextArea class="form-control" @bind-Value="newTraining.Description" />
                            </div>
                            <div class="mb-2">
                                <label>Date</label>
                                <InputDate class="form-control" @bind-Value="newTraining.Date" />
                            </div>
                            <div class="mb-2">
                                <label>Time</label>
                                <InputText class="form-control" @bind-Value="newTraining.Time" />
                            </div>
                            <div class="mb-2">
                                <label>Type</label>
                                <InputCheckbox @bind-Value="newTraining.IsOnline" /> Online
                            </div>
                            <div class="mb-2">
                                <label>Is Free?</label>
                                <InputCheckbox @bind-Value="newTraining.IsFree" />
                            </div>
                            @if (!newTraining.IsFree)
                            {
                                <div class="mb-2">
                                    <label>Fee</label>
                                    <InputNumber class="form-control" @bind-Value="newTraining.Fee" />
                                </div>
                            }
                            @if (!newTraining.IsOnline)
                            {
                                <div class="mb-2">
                                    <label>Venue</label>
                                    <select class="form-select" @bind="newTraining.VenueId">
                                        <option value="">Select a venue</option>
                                        @foreach (var venue in availableVenues)
                                        {
                                            <option value="@venue.Id">@venue.Name (@venue.Address) @(venue.IsFree ? "(Free)" : "")</option>
                                        }
                                    </select>
                                </div>
                                <div class="mb-2">
                                    <label>Location</label>
                                    <InputText class="form-control" @bind-Value="newTraining.Location" />
                                </div>
                            }
                            @if (newTraining.IsOnline)
                            {
                                <div class="mb-2">
                                    <label>Join Link</label>
                                    <InputText class="form-control" @bind-Value="newTraining.Link" />
                                </div>
                            }
                            <div class="mb-2">
                                <label>Additional Info</label>
                                <InputTextArea class="form-control" @bind-Value="newTraining.AdditionalInfo" />
                            </div>
                            <button type="submit" class="btn btn-primary w-100">Create</button>
                        </EditForm>
                    </div>
                </div>
            </div>
        </div>
    }

    <div class="mb-4 d-flex flex-wrap align-items-center gap-3">
        <input type="text" class="form-control w-auto" placeholder="Search by title or description..." @bind="searchTerm" />
        <select class="form-select w-auto" @bind="selectedType">
            <option value="">All Types</option>
            <option value="Online">Online</option>
            <option value="Physical">Physical</option>
        </select>
        <input type="text" class="form-control w-auto" @bind="selectedDate" @bind:event="onchange" />
        <button class="btn btn-secondary" @onclick="ClearFilters">Clear</button>
    </div>

    <div class="mb-5">
        <div class="row g-4">
            @if (upcomingEvents == null)
            {
                <div class="col-12 text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            }
            else foreach (var eventItem in FilteredEvents)
            {
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white">
                            <h5>@eventItem.Title</h5>
                        </div>
                        <div class="card-body">
                            <p><i class="fas fa-calendar me-2"></i> @eventItem.Date.ToString("dd MMM yyyy")</p>
                            <p><i class="fas fa-clock me-2"></i> @eventItem.Time</p>
                            <p><i class="fas fa-map-marker-alt me-2"></i> 
                                @eventItem.Location (@(eventItem.IsOnline ? "Online" : "In-Person"))
                            </p>
                            <p><i class="fas fa-tag me-2"></i> 
                                @(eventItem.IsFree ? "FREE" : $"${eventItem.Fee}")
                            </p>
                            @if (eventItem.IsOnline)
                            {
                                <a href="@eventItem.Link" class="btn btn-outline-primary w-100 mt-2" target="_blank" rel="noopener noreferrer">
                                    Join Online
                                </a>
                            }
                            else
                            {
                                <button class="btn btn-primary w-100 mt-2" 
                                        @onclick="() => RegisterForEvent(eventItem.Id)">
                                    Register Now
                                </button>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>

    <!-- Existing Training Materials Section -->
    @if (contents != null && contents.Any())
    {
        <div class="expert-trainings mt-5">
        <h2 class="text-center mb-4">Expert-Led Training Programs</h2>
        <div class="row g-4">
            @if (expertTrainings == null)
            {
                <div class="col-12 text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            }
            else foreach (var training in expertTrainings)
            {
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h5>@training.Title</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">@training.Description</p>
                            <p class="card-text"><strong>Expert:</strong> @training.ExpertName</p>
                            <p class="card-text"><strong>Duration:</strong> @training.Duration hours</p>
                        </div>
                        <div class="card-footer bg-transparent">
                            <button class="btn btn-primary w-100" @onclick="() => EnrollInTraining(training.Id)">
                                Enroll Now
                            </button>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>

    <div class="expert-trainings mt-5">
        <h2 class="text-center mb-4">Expert-Led Training Programs</h2>
        <div class="row g-4">
            @if (expertTrainings == null)
            {
                <div class="col-12 text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            }
            else foreach (var training in expertTrainings)
            {
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h5>@training.Title</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">@training.Description</p>
                            <p class="card-text"><strong>Expert:</strong> @training.ExpertName</p>
                            <p class="card-text"><strong>Duration:</strong> @training.Duration hours</p>
                        </div>
                        <div class="card-footer bg-transparent">
                            <button class="btn btn-primary w-100" @onclick="() => EnrollInTraining(training.Id)">
                                Enroll Now
                            </button>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>

    <div class="training-materials mt-5">
            <h2 class="text-center mb-4">Self-Paced Learning Materials</h2>
            <div class="row g-4">
                <!-- Existing content cards here -->
            </div>
        </div>
    }


    @if (isLoading)
        {
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        }

        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success">@successMessage</div>
        }

        @if (!string.IsNullOrEmpty(errorMessage))
        {
            <div class="alert alert-danger">@errorMessage</div>
        }
</div>

@code {
    private List<EducationalContent>? contents;
    private List<ExpertTraining>? expertTrainings;
    private List<TrainingEvent>? upcomingEvents;
    private List<Venue> availableVenues = new();
    private TrainingEvent newTraining = new();
    private string? errorMessage;
    private bool isLoading = false;
    private string? successMessage;
    private bool showCreateTrainingModal = false;
    private bool canCreateTraining = false;
    private string? currentUserId;
    private string? currentUserRole;

    // Filtering/search state
    private string searchTerm = string.Empty;
    private string selectedType = string.Empty;
    private string selectedDate = string.Empty;

    // Toggle for showing only my trainings
    private bool showMyTrainings = false;

    private IEnumerable<TrainingEvent> FilteredEvents =>
        (upcomingEvents ?? Enumerable.Empty<TrainingEvent>())
        .Where(e =>
            (string.IsNullOrWhiteSpace(searchTerm) || (e.Title?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true || e.Description?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true)) &&
            (string.IsNullOrEmpty(selectedType) || (selectedType == "Online" && e.IsOnline) || (selectedType == "Physical" && !e.IsOnline)) &&
            (string.IsNullOrEmpty(selectedDate) || (e.Date.ToString("yyyy-MM-dd") == selectedDate)) &&
            (!showMyTrainings || (e.OrganizerId == currentUserId))
        );

    private void ClearFilters()
    {
        searchTerm = string.Empty;
        selectedType = string.Empty;
        selectedDate = string.Empty;
    }

    private void ToggleShowMyTrainings()
    {
        showMyTrainings = !showMyTrainings;
    }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            isLoading = true;
            var authState = await AuthProvider.GetAuthenticationStateAsync();
            var user = authState.User;
            currentUserId = user.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            currentUserRole = user.FindFirst(System.Security.Claims.ClaimTypes.Role)?.Value;
            canCreateTraining = user.IsInRole("Expert") || user.IsInRole("Vendor");
            contents = await ContentService.GetContents();
            expertTrainings = await TrainingService.GetExpertTrainingsAsync();
            upcomingEvents = await TrainingService.GetUpcomingEventsAsync();
            availableVenues = await LoadVenuesAsync();
            successMessage = "Data loaded successfully!";
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading data: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }

    }
    private void HandleMyTrainingsClick()
    {
        Navigation.NavigateTo("/my-trainings");
    }
    private void ShowCreateTrainingModal()
    {
        newTraining = new TrainingEvent { Date = DateTime.Today, IsFree = true, IsOnline = true };
        showCreateTrainingModal = true;
    }
    private void CloseCreateTrainingModal()
    {
        showCreateTrainingModal = false;
    }

    private async Task<List<Venue>> LoadVenuesAsync()
{
    return await VenueService.GetVenuesAsync();
}

    private async Task HandleCreateTraining()
    {
        errorMessage = null;
        successMessage = null;
        isLoading = true;
        try
        {
            if (currentUserId == null || (!currentUserRole!.Equals("Expert") && !currentUserRole!.Equals("Vendor")))
            {
                errorMessage = "Only Experts or Vendors can create trainings.";
                return;
            }
            newTraining.OrganizerId = currentUserId;
            newTraining.OrganizerType = currentUserRole!;
            await TrainingService.CreateTrainingAsync(newTraining);
            successMessage = "Training created successfully!";
            showCreateTrainingModal = false;
            upcomingEvents = await TrainingService.GetUpcomingEventsAsync();
        }
        catch (Exception ex)
        {
            errorMessage = $"Error creating training: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }

 

    private async Task RegisterForEvent(int eventId)
    {
        await HandleRegistration(async (userId) => await TrainingService.RegisterForEventAsync(eventId, userId),
            successMsg: "Successfully registered for the event!",
            alreadyRegisteredMsg: "You are already registered for this event.");
    }

    // Generalized registration handler
    private async Task HandleRegistration(Func<string, Task> registrationAction, string successMsg, string alreadyRegisteredMsg)
    {
        try
        {
            isLoading = true;
            var authState = await AuthProvider.GetAuthenticationStateAsync();
            var userId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (userId != null)
            {
                await registrationAction(userId);
                successMessage = successMsg;
            }
        }
        catch (Exception ex)
        {
            if (ex is InvalidOperationException && ex.Message.Contains("already registered", StringComparison.OrdinalIgnoreCase))
            {
                errorMessage = alreadyRegisteredMsg;
            }
            else
            {
                errorMessage = $"Registration failed: {ex.Message}";
            }
        }
        finally
        {
            isLoading = false;
        }
    }
    private async Task ShareInsight()
    {
        await Task.Delay(1); // Ensure this method uses await
        Console.WriteLine("Sharing insight");
    }

    private async Task EnrollInTraining(int trainingId)
    {
        try
        {
            var authState = await AuthProvider.GetAuthenticationStateAsync();
            var userId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (userId != null)
            {
                var training = upcomingEvents?.FirstOrDefault(t => t.Id == trainingId);
                if (training != null && !training.IsFree)
                {
                    // Simulate payment prompt
                    var confirmed = await PromptPayment(training.Fee);
                    if (!confirmed)
                    {
                        errorMessage = "Payment was not completed.";
                        return;
                    }
                    await TrainingService.EnrollInTrainingAsync(trainingId, userId, true);
                    successMessage = "Successfully registered and payment received!";
                }
                else
                {
                    await TrainingService.EnrollInTrainingAsync(trainingId, userId, false);
                    successMessage = "Successfully registered for the free training!";
                }
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Error enrolling in training: {ex.Message}";
        }
    }

    // Simulated payment dialog (replace with real payment integration as needed)
    private async Task<bool> PromptPayment(decimal amount)
    {
        // In real app, show a modal or redirect to payment provider
        await Task.Delay(500); // Simulate delay
        return true; // Always "paid" for demo
    }
}


<style>
    /* Base Styles */
    .training-page {
        font-family: 'Segoe UI', system-ui, sans-serif;
        color: #333;
        line-height: 1.6;
    }
    
    .page-header {
        background: linear-gradient(135deg, #f5f7fa 0%, #e4efe9 100%);
        padding: 2rem 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .page-title {
        font-weight: 700;
        color: #2c3e50;
        margin: 0;
    }
    
    .page-subtitle {
        color: #7f8c8d;
        margin: 0.5rem 0 0;
    }
    
    /* Section Styles */
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    
    .section-title {
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }
    
    .mb-6 {
        margin-bottom: 4rem;
    }
    
    /* Card Grids */
    .card-grid, .program-grid, .resource-grid {
        display: grid;
        gap: 1.5rem;
    }
    
    .card-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
    
    .program-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
    
    .resource-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
    
    /* Card Styles */
    .event-card {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        transition: transform 0.2s, box-shadow 0.2s;
        display: flex;
        flex-direction: column;
    }
    
    .event-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.1);
    }
    
    .event-card.online .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .event-card.in-person .card-header {
        background: linear-gradient(135deg, #48c6ef 0%, #6f86d6 100%);
    }
    
    .card-header {
        padding: 0.75rem 1rem;
        display: flex;
        gap: 0.5rem;
    }
    
    .card-body {
        padding: 1.25rem;
        flex-grow: 1;
    }
    
    .card-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0 0 1rem;
    }
    
    .card-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.85rem;
        color: #7f8c8d;
    }
    
    .card-description {
        font-size: 0.9rem;
        color: #555;
        margin: 0;
    }
    
    .card-footer {
        padding: 1rem 1.25rem;
    }
    
    /* Badges */
    .badge {
        font-size: 0.7rem;
        font-weight: 600;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .badge-free {
        background: #2ecc71;
        color: white;
    }
    
    .badge-paid {
        background: #e74c3c;
        color: white;
    }
    
    .badge-online {
        background: rgba(255,255,255,0.2);
        color: white;
    }
    
    .badge-inperson {
        background: rgba(255,255,255,0.2);
        color: white;
    }
    
    /* Program Cards */
    .program-card {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        transition: transform 0.2s;
        display: flex;
        flex-direction: column;
    }
    
    .program-card:hover {
        transform: translateY(-5px);
    }
    
    .program-badge {
        background: #f8f9fa;
        padding: 0.5rem 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.8rem;
        color: #7f8c8d;
    }
    
    .program-content {
        padding: 1.25rem;
        flex-grow: 1;
    }
    
    .program-title {
        font-size: 1rem;
        font-weight: 600;
        margin: 0 0 0.75rem;
    }
    
    .program-description {
        font-size: 0.85rem;
        color: #555;
        margin: 0 0 1rem;
    }
    
    .program-meta {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }
    
    /* Resource Cards */
    .resource-card {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        display: flex;
        flex-direction: column;
    }
    
    .resource-icon {
        font-size: 1.5rem;
        color: #3498db;
        margin-bottom: 1rem;
    }
    
    .resource-title {
        font-size: 1rem;
        font-weight: 600;
        margin: 0 0 0.5rem;
    }
    
    .resource-description {
        font-size: 0.85rem;
        color: #7f8c8d;
        margin: 0 0 1rem;
    }
    
    /* Modal Styles */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }
    
    .modal-content {
        background: white;
        border-radius: 8px;
        width: 100%;
        max-width: 700px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }
    
    .modal-header {
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .modal-header h5 {
        margin: 0;
        font-weight: 600;
    }
    
    .close-btn {
        background: none;
        border: none;
        font-size: 1.25rem;
        color: #7f8c8d;
        cursor: pointer;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
    
    .modal-footer {
        padding: 1rem 1.5rem;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: flex-end;
        gap: 0.75rem;
    }
    
    /* Form Styles */
    .form-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-group.span-2 {
        grid-column: span 2;
    }
    
    label {
        display: block;
        font-size: 0.85rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #2c3e50;
    }
    
    .form-control, .form-select {
        width: 100%;
        padding: 0.5rem 0.75rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 0.9rem;
    }
    
    .form-check {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .validation-summary {
        color: #e74c3c;
        font-size: 0.85rem;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: #fdeded;
        border-radius: 4px;
    }
    
    /* Utility Classes */
    .loading-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem;
        background: #f8f9fa;
        border-radius: 8px;
        color: #7f8c8d;
    }
    
    .empty-state i {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: #bdc3c7;
    }
    
    .status-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255,255,255,0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }
    
    /* Responsive Adjustments */
    @@media (max-width: 768px) {
        .form-grid {
            grid-template-columns: 1fr;
        }
        
        .form-group.span-2 {
            grid-column: span 1;
        }
        
        .card-grid, .program-grid, .resource-grid {
            grid-template-columns: 1fr;
        }
        
        .section-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
    }
</style>