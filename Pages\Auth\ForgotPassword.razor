@page "/forgot-password"
@using AgriEnergyPlatform.Services
@using System.ComponentModel.DataAnnotations
@inject IAuthService AuthService
@inject NavigationManager NavigationManager

<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center align-items-center" style="min-height: 100vh;">
            <div class="col-lg-10">
                <div class="card auth-card shadow-lg">
                    <div class="row g-0">
                        <!-- Left side with image -->
                        <div class="col-md-6 d-none d-md-block">
                            <div class="auth-image h-100">
                                <img src="images/5.jpg" 
                                     alt="Sustainable farming" 
                                     class="img-fluid h-100 w-100 object-fit-cover rounded-start">
                                <div class="image-overlay p-4">
                                    <h2 class="text-white">Password Reset</h2>
                                    <p class="text-white-50">We'll help you get back to your account</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Right side with form -->
                        <div class="col-md-6">
                            <div class="card-body p-4 p-md-5">
                                <div class="text-center mb-4">
                                    <i class="fas fa-key text-primary mb-3" style="font-size: 2.5rem;"></i>
                                    <h2>Forgot Password</h2>
                                    <p class="text-muted">Enter your email to receive a temporary password</p>
                                </div>

                                @if (!string.IsNullOrEmpty(message))
                                {
                                    <div class="alert @(isSuccess ? "alert-success" : "alert-danger") d-flex align-items-center">
                                        <i class="@(isSuccess ? "fas fa-check-circle" : "fas fa-exclamation-circle") me-2"></i>
                                        <div>@message</div>
                                    </div>
                                }

                                <EditForm Model="model" OnValidSubmit="HandleSubmit">
                                    <DataAnnotationsValidator />

                                    <div class="mb-4">
                                        <label class="form-label">Email Address</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light">
                                                <i class="fas fa-envelope text-muted"></i>
                                            </span>
                                            <InputText @bind-Value="model.Email" class="form-control" placeholder="Enter your email" />
                                        </div>
                                        <ValidationMessage For="@(() => model.Email)" class="text-danger small" />
                                    </div>

                                    <button type="submit" class="btn btn-primary btn-lg w-100 mb-3" disabled="@isProcessing">
                                        @if (isProcessing)
                                        {
                                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                            <span>Processing...</span>
                                        }
                                        else
                                        {
                                            <i class="fas fa-paper-plane me-2"></i>
                                            <span>Send Reset Link</span>
                                        }
                                    </button>

                                    <div class="text-center mt-4">
                                        <p class="text-muted">
                                            Remember your password? 
                                            <a href="/login" class="text-decoration-none fw-bold">Back to Login</a>
                                        </p>
                                    </div>
                                </EditForm>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .auth-container {
        background-color: #f8f9fa;
        min-height: 100vh;
    }
    
    .auth-card {
        border: none;
        border-radius: 1rem;
        overflow: hidden;
    }
    
    .auth-image {
        position: relative;
    }
    
    .image-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0,0,0,0.7));
    }
    
    .form-control:focus {
        border-color: #2e7d32;
        box-shadow: 0 0 0 0.25rem rgba(46, 125, 50, 0.25);
    }
    
    .btn-primary {
        background-color: #2e7d32;
        border-color: #2e7d32;
    }
    
    .btn-primary:hover {
        background-color: #1b5e20;
        border-color: #1b5e20;
    }
</style>


@code {
    private ForgotPasswordModel model = new();
    private string message = string.Empty;
    private bool isSuccess;
    private bool isProcessing;

    private async Task HandleSubmit()
    {
        isProcessing = true;
        message = string.Empty;

        try
        {
            var result = await AuthService.InitiatePasswordReset(model.Email);
            if (result)
            {
                isSuccess = true;
                message = "If an account exists with this email, you will receive a temporary password shortly.";
                model = new ForgotPasswordModel();
            }
            else
            {
                isSuccess = false;
                message = "If an account exists with this email, you will receive a temporary password shortly.";
            }
        }
        catch (Exception)
        {
            isSuccess = false;
            message = "An error occurred. Please try again later.";
        }
        finally
        {
            isProcessing = false;
        }
    }

    public class ForgotPasswordModel
    {
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string Email { get; set; } = string.Empty;
    }
}