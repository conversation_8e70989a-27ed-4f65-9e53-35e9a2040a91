# AgriEnergyPlatform

## Description
AgriEnergyPlatform is a comprehensive application designed to manage agricultural energy data efficiently. It provides tools for data entry, analysis, and reporting, aimed at improving energy management in agriculture.

## Installation
1. Clone the repository:
   ```bash
   git clone https://github.com/mugash70/AgriEnergyPlatform.git
   ```
2. Navigate to the project directory:
   ```bash
   cd AgriEnergyPlatform
   ```
3. Restore the dependencies:
   ```bash
   dotnet restore
   ```
4. Run the application:
   ```bash
   dotnet run
   ```

## Usage
Access the application via your web browser at `http://localhost:5000`. Follow the on-screen instructions to navigate through the application.

## Project Structure
- **Pages/**: Contains Razor pages for the user interface.
- **Models/**: Contains data models used in the application.
- **Services/**: Contains business logic and services.
- **Migrations/**: Contains database migration files.
- **wwwroot/**: Contains static files such as CSS, JavaScript, and images.

## Contributing
Contributions are welcome! Please submit a pull request or open an issue for any enhancements or bug fixes.

## License
This project is licensed under the MIT License. See the LICENSE file for details.
