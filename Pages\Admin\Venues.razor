@page "/admin/venues"
@attribute [Authorize(Roles = "Admin")]
@using AgriEnergyPlatform.Models
@using AgriEnergyPlatform.Services
@inject IVenueService VenueService

<h2>Manage Venues</h2>

@if (isLoading)
{
    <div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>
}
else
{
    <button class="btn btn-success mb-3" @onclick="ShowAddVenue">Add Venue</button>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Name</th>
                <th>Address</th>
                <th>Is Free</th>
                <th>Contact</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var venue in venues)
            {
                <tr>
                    <td>@venue.Name</td>
                    <td>@venue.Address</td>
                    <td>@(venue.IsFree ? "Yes" : "No")</td>
                    <td>@venue.ContactInfo</td>
                    <td>
                        <button class="btn btn-sm btn-primary me-2" @onclick="() => EditVenue(venue)">Edit</button>
                        <button class="btn btn-sm btn-danger" @onclick="() => DeleteVenue(venue.Id)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}

@if (showVenueModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background:rgba(0,0,0,0.3);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@((editingVenue?.Id ?? 0) == 0 ? "Add Venue" : "Edit Venue")</h5>
                    <button type="button" class="btn-close" @onclick="CloseVenueModal"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="editingVenue" OnValidSubmit="HandleVenueSubmit">
                        <DataAnnotationsValidator />
                        <ValidationSummary />
                        <div class="mb-2">
                            <label>Name</label>
                            <InputText class="form-control" @bind-Value="editingVenue.Name" />
                        </div>
                        <div class="mb-2">
                            <label>Address</label>
                            <InputText class="form-control" @bind-Value="editingVenue.Address" />
                        </div>
                        <div class="mb-2">
                            <label>Contact Info</label>
                            <InputText class="form-control" @bind-Value="editingVenue.ContactInfo" />
                        </div>
                        <div class="mb-2">
                            <label>Is Free?</label>
                            <InputCheckbox @bind-Value="editingVenue.IsFree" />
                        </div>
                        <div class="mb-2">
                            <label>Notes</label>
                            <InputTextArea class="form-control" @bind-Value="editingVenue.Notes" />
                        </div>
                        <button type="submit" class="btn btn-primary w-100">Save</button>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<Venue> venues = new();
    private Venue editingVenue = new Venue();
    private bool showVenueModal = false;
    private bool isLoading = false;

    protected override async Task OnInitializedAsync()
    {
        isLoading = true;
        venues = await VenueService.GetVenuesAsync();
        isLoading = false;
    }
    private void ShowAddVenue()
    {
        editingVenue = new Venue();
        showVenueModal = true;
    }
    private void EditVenue(Venue venue)
    {
        editingVenue = new Venue
        {
            Id = venue.Id,
            Name = venue.Name,
            Address = venue.Address,
            ContactInfo = venue.ContactInfo,
            IsFree = venue.IsFree,
            Notes = venue.Notes
        };
        showVenueModal = true;
    }
    private async Task HandleVenueSubmit()
    {
        if (editingVenue.Id == 0)
        {
            await VenueService.AddVenueAsync(editingVenue);
        }
        else
        {
            await VenueService.UpdateVenueAsync(editingVenue);
        }
        venues = await VenueService.GetVenuesAsync();
        showVenueModal = false;
    }
    private void CloseVenueModal()
    {
        showVenueModal = false;
    }
    private async Task DeleteVenue(int id)
    {
        await VenueService.DeleteVenueAsync(id);
        venues = await VenueService.GetVenuesAsync();
    }
}
