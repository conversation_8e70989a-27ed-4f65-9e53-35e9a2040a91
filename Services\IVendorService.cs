using AgriEnergyPlatform.Models;
using System.Threading.Tasks;

namespace AgriEnergyPlatform.Services
{
    public interface IVendorService
    {
        Task<Vendor?> GetVendorByApplicationUserIdAsync(string applicationUserId);
        Task<Vendor?> GetVendorByIdAsync(int vendorId);
        Task<List<Vendor>> GetAllVendorsAsync();
        Task<Vendor> CreateVendorAsync(Vendor vendor);
        Task<bool> UpdateVendorAsync(Vendor vendor);
        // Add other vendor-related methods as needed
    }
}