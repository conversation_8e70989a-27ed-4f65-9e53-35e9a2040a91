﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AgriEnergyPlatform.Migrations
{
    /// <inheritdoc />
    public partial class AddIsPublishedToForumTopic : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsPublished",
                table: "ForumTopics",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "ForumTopicId",
                table: "Comments",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Comments_ForumTopicId",
                table: "Comments",
                column: "ForumTopicId");

            migrationBuilder.AddForeignKey(
                name: "FK_Comments_ForumTopics_ForumTopicId",
                table: "Comments",
                column: "ForumTopicId",
                principalTable: "ForumTopics",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Comments_ForumTopics_ForumTopicId",
                table: "Comments");

            migrationBuilder.DropIndex(
                name: "IX_Comments_ForumTopicId",
                table: "Comments");

            migrationBuilder.DropColumn(
                name: "IsPublished",
                table: "ForumTopics");

            migrationBuilder.DropColumn(
                name: "ForumTopicId",
                table: "Comments");
        }
    }
}
