<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="wind-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#34A853;stop-opacity:0.2"/>
      <stop offset="100%" style="stop-color:#34A853;stop-opacity:0.1"/>
    </linearGradient>
  </defs>
  <g fill="none" fill-rule="evenodd">
    <rect x="95" y="60" width="10" height="100" fill="#34A853"/>
    <g transform="translate(100,60) rotate(0)">
      <path d="M0,0 L-60,-20 L-50,-5 Z" fill="#34A853" opacity="0.8"/>
      <path d="M0,0 L60,-20 L50,-5 Z" fill="#34A853" opacity="0.8"/>
      <path d="M0,0 L0,60 L10,50 Z" fill="#34A853" opacity="0.8"/>
    </g>
    <circle cx="100" cy="60" r="8" fill="#34A853"/>
  </g>
</svg>