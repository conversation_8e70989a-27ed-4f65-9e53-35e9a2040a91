using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using AgriEnergyPlatform.Data;
using AgriEnergyPlatform.Models;
using System.Net.Mail;
using System.Net;
using System.Text;
using Microsoft.AspNetCore.Identity;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;

namespace AgriEnergyPlatform.Services
{
    public class AuthService : IAuthService
    {
        // Replace with a 32+ character secret (256+ bits)
        private readonly string _jwtSecret = "YourVerySecureSecretKeyWithAtLeast32Characters123!";
        private readonly ApplicationDbContext _context;
        private readonly Dictionary<string, User> _activeSessions = new();
        private readonly PasswordHasher _passwordHasher;

        public AuthService(ApplicationDbContext context)
        {
            _context = context;
            _passwordHasher = new PasswordHasher();
        }

        public async Task<(ApplicationUser? user, string? token)> Login(string email, string password)
        {
            var user = await _context.Users.FirstOrDefaultAsync(u =>
                u.Email != null && u.Email.ToLower().Trim() == email.ToLower().Trim());
            
            if (user == null || user.PasswordHash == null || !_passwordHasher.VerifyPassword(password, user.PasswordHash))
                return (null, null);
            
            try 
            {
                var token = GenerateJwtToken(user);
                return (user, token);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Token generation failed: {ex.Message}");
                return (user, null);
            }
        }

        private string GenerateJwtToken(ApplicationUser user)
        {
            if (user == null) 
                throw new ArgumentNullException(nameof(user), "User cannot be null for token generation");
            
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_jwtSecret);
    
            // Validate key length
            if (key.Length < 32)
            {
                throw new ArgumentException("JWT secret must be at least 256 bits (32 characters)");
            }
    
            var claims = new[]
            {
                new Claim(ClaimTypes.Email, user.Email ?? string.Empty),
                new Claim(ClaimTypes.Name, user.UserName ?? string.Empty),
                new Claim(ClaimTypes.NameIdentifier, user.Id ?? string.Empty),
                new Claim(ClaimTypes.Role, user.Role ?? "User")
            };
    
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddHours(8),
                SigningCredentials = new SigningCredentials(
                    new SymmetricSecurityKey(key), 
                    SecurityAlgorithms.HmacSha256Signature)
            };
    
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        public ClaimsPrincipal? ValidateJwtToken(string? token)
        {
            if (string.IsNullOrEmpty(token))
                return null;

            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_jwtSecret);

            try
            {
                var principal = tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                return principal;
            }
            catch
            {
                return null;
            }
        }

        public bool IsUserAuthenticated(string sessionToken)
        {
            return _activeSessions.ContainsKey(sessionToken);
        }

        public User? GetAuthenticatedUser(string sessionToken)
        {
            return _activeSessions.TryGetValue(sessionToken, out var user) ? user : null;
        }

        public void Logout(string sessionToken)
        {
            _activeSessions.Remove(sessionToken);
        }
    
        public async Task<bool> Register(ApplicationUser user, string password)
        {
            if (await _context.Users.AnyAsync(u => u.Email == user.Email))
                return false;
    
            user.PasswordHash = HashPassword(password);
            _context.Users.Add(user);
            await _context.SaveChangesAsync(); // Save ApplicationUser first to get its Id

            if (user.Role == "Vendor")
            {
                var vendor = new Vendor
                {
                    UserId = user.Id, // Link to the ApplicationUser
                    ApplicationUser = user,
                    CompanyName = user.CompanyName ?? "Default Company Name", // Or handle if null
                    ContactEmail = user.ContactEmail ?? string.Empty,
                    ContactPhone = user.ContactPhoneNumber ?? string.Empty,
                    IsVerified = false, // Default to not verified
                    RegisteredDate = DateTime.UtcNow
                    // BusinessType, LicenseNumber, TaxId, Website can be set later or made optional
                };
                _context.Vendors.Add(vendor);
                await _context.SaveChangesAsync();
            }
            return true;
        }
    
        public async Task<bool> ChangePassword(string userId, string oldPassword, string newPassword)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null || user.PasswordHash == null) return false;
    
            if (!VerifyPassword(oldPassword, user.PasswordHash))
                return false;
    
            user.PasswordHash = HashPassword(newPassword);
            await _context.SaveChangesAsync();
            return true;
        }

        public string HashPassword(string password)
        {
            return _passwordHasher.HashPassword(password);
        }

        public bool VerifyPassword(string password, string passwordHash)
        {
            return _passwordHasher.VerifyPassword(passwordHash, password);
        }

        public async Task<bool> InitiatePasswordReset(string email)
        {
            var user = await _context.Users.FirstOrDefaultAsync(u => 
                u.Email != null && u.Email.ToLower().Trim() == email.ToLower().Trim());
            if (user == null) return false;
        
            // Generate temporary password
            var tempPassword = GenerateTemporaryPassword();
            user.PasswordHash = HashPassword(tempPassword);
            user.RequirePasswordChange = true;
            
            await _context.SaveChangesAsync();
            
            // Send email
            if (email != null)
            {
                await SendPasswordResetEmail(email, tempPassword);
            }
            
            return true;
        }
    
        private string GenerateTemporaryPassword()
        {
            const string chars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, 8)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
    
        private async Task SendPasswordResetEmail(string email, string tempPassword)
        {
            var smtpClient = new SmtpClient("smtp.gmail.com")
            {
                Port = 587,
                Credentials = new NetworkCredential("<EMAIL>", "gyfo hpbr zsku vvhd"),
                EnableSsl = true,
            };
    
            var mailMessage = new MailMessage
            {
                From = new MailAddress("<EMAIL>"),
                Subject = "Password Reset",
                Body = $"Your temporary password is: {tempPassword}\nPlease change your password after logging in.",
                IsBodyHtml = false,
            };
            mailMessage.To.Add(email);
    
            await smtpClient.SendMailAsync(mailMessage);
        }

        public ApplicationUser? GetCurrentUser(HttpContext httpContext)
        {
            var email = httpContext.User.Identity?.Name;
            if (string.IsNullOrEmpty(email)) return null;
            
            return _context.Users.FirstOrDefault(u => u.Email == email);
        }
    }
}