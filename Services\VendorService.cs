using AgriEnergyPlatform.Data;
using AgriEnergyPlatform.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AgriEnergyPlatform.Services
{
    public class VendorService : IVendorService
    {
        private readonly ApplicationDbContext _context;

        public VendorService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Vendor?> GetVendorByApplicationUserIdAsync(string applicationUserId)
        {
            if (string.IsNullOrEmpty(applicationUserId))
            {
                return null;
            }
            return await _context.Vendors
                                 .Include(v => v.ApplicationUser) // Optional: include if you need ApplicationUser details
                                 .FirstOrDefaultAsync(v => v.UserId == applicationUserId);
        }

        public async Task<Vendor?> GetVendorByIdAsync(int vendorId)
        {
            return await _context.Vendors
                                 .Include(v => v.ApplicationUser)
                                 .FirstOrDefaultAsync(v => v.Id == vendorId);
        }

        public async Task<List<Vendor>> GetAllVendorsAsync()
        {
            return await _context.Vendors.Include(v => v.ApplicationUser).ToListAsync();
        }

        public async Task<Vendor> CreateVendorAsync(Vendor vendor)
        {
            if (vendor == null) throw new ArgumentNullException(nameof(vendor));

            // Ensure UserId is set if ApplicationUser is provided and UserId is not
            if (vendor.ApplicationUser != null && string.IsNullOrEmpty(vendor.UserId))
            {
                vendor.UserId = vendor.ApplicationUser.Id;
            }

            _context.Vendors.Add(vendor);
            await _context.SaveChangesAsync();
            return vendor;
        }

        public async Task<bool> UpdateVendorAsync(Vendor vendor)
        {
            if (vendor == null) throw new ArgumentNullException(nameof(vendor));

            _context.Vendors.Update(vendor);
            try
            {
                await _context.SaveChangesAsync();
                return true;
            }
            catch (DbUpdateConcurrencyException)
            {
                // Handle concurrency issues if necessary
                return false;
            }
            catch (DbUpdateException)
            {
                // Handle other update errors
                return false;
            }
        }
    }
}