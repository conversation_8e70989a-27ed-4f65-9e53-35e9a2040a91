﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AgriEnergyPlatform.Migrations
{
    /// <inheritdoc />
    public partial class UpdateTopicLikeToForumTopicFK : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Comments_ForumTopics_ForumTopicId",
                table: "Comments");

            migrationBuilder.DropForeignKey(
                name: "FK_Comments_Topics_TopicId",
                table: "Comments");

            migrationBuilder.DropForeignKey(
                name: "FK_TopicLikes_Topics_TopicId",
                table: "TopicLikes");

            migrationBuilder.RenameColumn(
                name: "TopicId",
                table: "TopicLikes",
                newName: "ForumTopicId");

            migrationBuilder.RenameIndex(
                name: "IX_TopicLikes_TopicId",
                table: "TopicLikes",
                newName: "IX_TopicLikes_ForumTopicId");

            migrationBuilder.AlterColumn<int>(
                name: "TopicId",
                table: "Comments",
                type: "INTEGER",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "INTEGER");

            migrationBuilder.AlterColumn<int>(
                name: "ForumTopicId",
                table: "Comments",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "INTEGER",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Comments_ForumTopics_ForumTopicId",
                table: "Comments",
                column: "ForumTopicId",
                principalTable: "ForumTopics",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Comments_Topics_TopicId",
                table: "Comments",
                column: "TopicId",
                principalTable: "Topics",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TopicLikes_ForumTopics_ForumTopicId",
                table: "TopicLikes",
                column: "ForumTopicId",
                principalTable: "ForumTopics",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Comments_ForumTopics_ForumTopicId",
                table: "Comments");

            migrationBuilder.DropForeignKey(
                name: "FK_Comments_Topics_TopicId",
                table: "Comments");

            migrationBuilder.DropForeignKey(
                name: "FK_TopicLikes_ForumTopics_ForumTopicId",
                table: "TopicLikes");

            migrationBuilder.RenameColumn(
                name: "ForumTopicId",
                table: "TopicLikes",
                newName: "TopicId");

            migrationBuilder.RenameIndex(
                name: "IX_TopicLikes_ForumTopicId",
                table: "TopicLikes",
                newName: "IX_TopicLikes_TopicId");

            migrationBuilder.AlterColumn<int>(
                name: "TopicId",
                table: "Comments",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "INTEGER",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "ForumTopicId",
                table: "Comments",
                type: "INTEGER",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "INTEGER");

            migrationBuilder.AddForeignKey(
                name: "FK_Comments_ForumTopics_ForumTopicId",
                table: "Comments",
                column: "ForumTopicId",
                principalTable: "ForumTopics",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Comments_Topics_TopicId",
                table: "Comments",
                column: "TopicId",
                principalTable: "Topics",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TopicLikes_Topics_TopicId",
                table: "TopicLikes",
                column: "TopicId",
                principalTable: "Topics",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
