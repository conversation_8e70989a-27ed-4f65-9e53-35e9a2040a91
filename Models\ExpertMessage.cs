using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AgriEnergyPlatform.Models
{
    public class ExpertMessage
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ExpertId { get; set; }
        public Expert Expert { get; set; } = null!;

        [Required]
        public string SenderUserId { get; set; } = string.Empty;
        public ApplicationUser? Sender { get; set; }

        [Required]
        public string Message { get; set; } = string.Empty;

        public DateTime SentAt { get; set; } = DateTime.UtcNow;
        public bool IsRead { get; set; } = false;
    }
}
