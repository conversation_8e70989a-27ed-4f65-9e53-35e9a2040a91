@using System.Security.Claims  
@page "/insights/new"
@using AgriEnergyPlatform.Models
@using AgriEnergyPlatform.Services
@inject IForumService ForumService
@inject NavigationManager NavigationManager
@inject AuthenticationStateProvider AuthProvider

<div class="create-insight-page">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Main Card -->
                <div class="card insight-card shadow-lg">
                    <div class="card-header bg-gradient-success text-white">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="fas fa-lightbulb me-3 fs-2"></i>
                            <h3 class="mb-0">Share Your Insight</h3>
                        </div>
                    </div>
                    
                    <div class="card-body p-4 p-md-5">
                        <div class="text-center mb-4">
                            <p class="text-muted lead">
                                Contribute to the Agri-Energy community with your knowledge and experiences
                            </p>
                        </div>

                        <EditForm Model="newTopic" OnValidSubmit="HandleSubmit">
                            <DataAnnotationsValidator />
                            
                            @if (!string.IsNullOrEmpty(message))
                            {
                                <div class="alert @(isSuccess ? "alert-success" : "alert-danger") alert-dismissible fade show mb-4">
                                    @message
                                    <button type="button" class="btn-close" @onclick="() => message = string.Empty"></button>
                                </div>
                            }

                            <!-- Title Field -->
                            <div class="mb-4 form-group">
                                <label class="form-label fw-bold">Title *</label>
                                <InputText @bind-Value="newTopic.Title" 
                                         class="form-control form-control-lg" 
                                         placeholder="What's your insight about?" />
                                <ValidationMessage For="@(() => newTopic.Title)" class="text-danger small mt-1" />
                                <small class="form-text text-muted">Keep it clear and concise</small>
                            </div>

                            <!-- Description Field -->
                            <div class="mb-4 form-group">
                                <label class="form-label fw-bold">Details *</label>
                                <InputTextArea @bind-Value="newTopic.Description" 
                                             class="form-control" 
                                             rows="8"
                                             placeholder="Share your thoughts, experiences, or findings..."></InputTextArea>
                                <ValidationMessage For="@(() => newTopic.Description)" class="text-danger small mt-1" />
                                <small class="form-text text-muted">Markdown formatting supported</small>
                            </div>

                            <!-- Submit Button -->
                            <div class="d-grid mt-5">
                                <button type="submit" class="btn btn-success btn-lg py-3" disabled="@isProcessing">
                                    @if (isProcessing)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    }
                                    else
                                    {
                                        <i class="fas fa-paper-plane me-2"></i>
                                    }
                                    @((isProcessing ? "Publishing..." : "Publish Insight"))
                                </button>
                            </div>
                        </EditForm>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</div>

<style>
    .create-insight-page {
        background-color: #f8f9fa;
        min-height: calc(100vh - 120px);
    }

    .insight-card {
        border-radius: 12px;
        border: none;
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .insight-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0,0,0,0.1) !important;
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
        padding: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-control, .form-control-lg {
        border: 1px solid #ced4da;
        border-radius: 8px;
        padding: 0.75rem 1.25rem;
        font-size: 1rem;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    .form-control-lg {
        font-size: 1.1rem;
    }

    .form-control:focus, .form-control-lg:focus {
        border-color: #2e7d32;
        box-shadow: 0 0 0 0.25rem rgba(46, 125, 50, 0.15);
    }

    textarea.form-control {
        min-height: 200px;
    }

    .btn-success {
        background-color: #2e7d32;
        border-color: #2e7d32;
        font-weight: 600;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
    }

    .btn-success:hover {
        background-color: #1b5e20;
        border-color: #1b5e20;
        transform: translateY(-2px);
    }

    .btn-success:active {
        transform: translateY(0);
    }

    .alert {
        border-radius: 8px;
    }

    @@media (max-width: 768px) {
        .card-body {
            padding: 1.5rem;
        }
        
        .bg-gradient-success {
            padding: 1rem;
        }
    }
</style>

@code {
    private ForumTopic newTopic = new();
    private bool isProcessing = false;
    private bool isSuccess = false;
    private string message = string.Empty;

    private async Task HandleSubmit()
    {
        isProcessing = true;
        message = string.Empty;
    
        try
        {
            var authState = await AuthProvider.GetAuthenticationStateAsync();
            var userId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            if (userId != null)
            {
                await ForumService.AddTopicAsync(newTopic, userId);
                isSuccess = true;
                message = "Insight published successfully! Redirecting...";
                await Task.Delay(1500);
                NavigationManager.NavigateTo("/insights");
            }
            else
            {
                isSuccess = false;
                message = "You need to be logged in to share insights. Please login first.";
            }
        }
        catch (Exception ex)
        {
            isSuccess = false;
            message = $"Error: {ex.Message}";
        }
        finally
        {
            isProcessing = false;
        }
    }
}