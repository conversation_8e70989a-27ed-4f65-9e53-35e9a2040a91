using System.ComponentModel.DataAnnotations;

namespace AgriEnergyPlatform.Models
{
    public class User
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;
        
        [Required]
        public string PasswordHash { get; set; } = string.Empty;
        
        [Required]
        public string Name { get; set; } = string.Empty;
        
        public string PhoneNumber { get; set; } = string.Empty;
        
        public string ImageUrl { get; set; } = string.Empty;
        
        [Required]
        public string Role { get; set; } = "User"; // Default role
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? LastLogin { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public bool RequirePasswordChange { get; set; } = false;
    }
}