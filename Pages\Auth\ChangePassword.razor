@page "/change-password"
@using AgriEnergyPlatform.Services
@using System.ComponentModel.DataAnnotations
@using System.Security.Claims
@inject IAuthService AuthService
@inject NavigationManager NavigationManager
@inject AuthenticationStateProvider AuthProvider

<div class="container">
    <div class="row justify-content-center mt-5">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-body p-5">
                    <h2 class="text-center mb-4">Change Password</h2>
                    
                    @if (!string.IsNullOrEmpty(error))
                    {
                        <div class="alert alert-danger">@error</div>
                    }

                    <EditForm Model="model" OnValidSubmit="HandleSubmit">
                        <DataAnnotationsValidator />

                        <div class="mb-3">
                            <label class="form-label">Current Password</label>
                            <InputText type="password" @bind-Value="model.CurrentPassword" class="form-control" />
                            <ValidationMessage For="@(() => model.CurrentPassword)" />
                        </div>

                        <div class="mb-3">
                            <label class="form-label">New Password</label>
                            <InputText type="password" @bind-Value="model.NewPassword" class="form-control" />
                            <ValidationMessage For="@(() => model.NewPassword)" />
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Confirm New Password</label>
                            <InputText type="password" @bind-Value="model.ConfirmPassword" class="form-control" />
                            <ValidationMessage For="@(() => model.ConfirmPassword)" />
                        </div>

                        <button type="submit" class="btn btn-primary w-100">Change Password</button>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string userId = string.Empty; // Changed from int to string
    private ChangePasswordModel model = new();
    private string error = string.Empty;

    private async Task HandleSubmit()
    {
        if (model.NewPassword != model.ConfirmPassword)
        {
            error = "New passwords do not match";
            return;
        }

        try
        {
            // Get the actual user ID from authentication state
            var authState = await AuthProvider.GetAuthenticationStateAsync();
            var userId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            if (string.IsNullOrEmpty(userId))
            {
                error = "User not authenticated";
                return;
            }

            var result = await AuthService.ChangePassword(userId, model.CurrentPassword, model.NewPassword);
            if (result)
            {
                NavigationManager.NavigateTo("/");
            }
            else
            {
                error = "Current password is incorrect";
            }
        }
        catch
        {
            error = "An error occurred. Please try again.";
        }
    }

    public class ChangePasswordModel
    {
        [Required]
        public string CurrentPassword { get; set; } = string.Empty;

        [Required]
        [MinLength(6)]
        public string NewPassword { get; set; } = string.Empty;

        [Required]
        [Compare(nameof(NewPassword))]
        public string ConfirmPassword { get; set; } = string.Empty;
    }
}