using System.Collections.Generic;
using System.Threading.Tasks;
using AgriEnergyPlatform.Data;
using AgriEnergyPlatform.Models;
using Microsoft.EntityFrameworkCore;

namespace AgriEnergyPlatform.Services
{
    public class TrainingService : 
    ITrainingService
    {
        private readonly ApplicationDbContext _context;

        public TrainingService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<TrainingEvent>> GetUpcomingEventsAsync()
        {
            return await _context.TrainingEvents
                .Where(e => e.Date >= DateTime.Today)
                .OrderBy(e => e.Date)
                .ToListAsync();
        }

        public async Task EnrollInTrainingAsync(int trainingId, string userId, bool isPaid = false)
        {
            // Check if user is already enrolled
            var existingEnrollment = await _context.TrainingEnrollments
                .FirstOrDefaultAsync(e => e.TrainingId == trainingId && e.UserId == userId);
            if (existingEnrollment != null)
            {
                throw new InvalidOperationException("User is already enrolled in this training");
            }
            // Create new enrollment
            var enrollment = new TrainingEnrollment
            {
                TrainingId = trainingId,
                UserId = userId,
                EnrollmentDate = DateTime.UtcNow,
                PaymentStatus = isPaid ? "Paid" : "Free",
                PaymentDate = isPaid ? DateTime.UtcNow : null
            };
            _context.TrainingEnrollments.Add(enrollment);
            await _context.SaveChangesAsync();
        }
    
        public async Task<List<ExpertTraining>> GetExpertTrainingsAsync()
        {
            return await _context.ExpertTrainings
                .Include(t => t.Expert)
                .OrderBy(t => t.Title)
                .ToListAsync();
        }
        public async Task RegisterForEventAsync(int eventId, string userId)
        {
            // Check if user is already registered for the event
            var existingRegistration = await _context.EventRegistrations
                .FirstOrDefaultAsync(r => r.EventId == eventId && r.UserId == userId);
            if (existingRegistration != null)
            {
                throw new InvalidOperationException("User is already registered for this event");
            }
            // Create new registration
            var registration = new EventRegistration
            {
                EventId = eventId,
                UserId = userId,
                RegisteredAt = DateTime.UtcNow
            };
            _context.EventRegistrations.Add(registration);
            await _context.SaveChangesAsync();
        }
        public async Task CreateTrainingAsync(TrainingEvent training)
        {
            _context.TrainingEvents.Add(training);
            await _context.SaveChangesAsync();
        }
        public async Task<List<TrainingEnrollment>> GetUserEnrollmentsAsync(string userId)
        {
            return await _context.TrainingEnrollments
                .Where(e => e.UserId == userId)
                .ToListAsync();
        }
        public async Task<List<TrainingEnrollment>> GetRegistrationsForEventAsync(int trainingId)
        {
            return await _context.TrainingEnrollments
                .Where(e => e.TrainingId == trainingId)
                .ToListAsync();
        }
        public async Task CancelEnrollmentAsync(int trainingId, string userId)
        {
            var enrollment = await _context.TrainingEnrollments.FirstOrDefaultAsync(e => e.TrainingId == trainingId && e.UserId == userId);
            if (enrollment != null)
            {
                _context.TrainingEnrollments.Remove(enrollment);
                await _context.SaveChangesAsync();
            }
        }
    }
}