using System.Collections.Generic;
using System.Threading.Tasks;
using AgriEnergyPlatform.Models;

namespace AgriEnergyPlatform.Services
{
    public interface ITrainingService
    {
        Task<List<TrainingEvent>> GetUpcomingEventsAsync();
        Task EnrollInTrainingAsync(int trainingId, string userId, bool isPaid = false);
        Task<List<ExpertTraining>> GetExpertTrainingsAsync();
        Task RegisterForEventAsync(int eventId, string userId);
        Task CreateTrainingAsync(TrainingEvent training);
        Task<List<TrainingEnrollment>> GetUserEnrollmentsAsync(string userId);
        Task<List<TrainingEnrollment>> GetRegistrationsForEventAsync(int trainingId);
        Task CancelEnrollmentAsync(int trainingId, string userId);
    }
}