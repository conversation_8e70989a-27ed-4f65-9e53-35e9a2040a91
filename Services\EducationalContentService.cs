using Microsoft.EntityFrameworkCore;
using AgriEnergyPlatform.Data;
using AgriEnergyPlatform.Models;

namespace AgriEnergyPlatform.Services
{
    public class EducationalContentService : IEducationalContentService
    {
        private readonly ApplicationDbContext _context;

        public EducationalContentService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<EducationalContent>> GetContents(string? type = null, string? category = null)
        {
            var query = _context.EducationalContents.AsQueryable();
            
            if (!string.IsNullOrEmpty(type))
                query = query.Where(c => c.Type == type);
                
            if (!string.IsNullOrEmpty(category))
                query = query.Where(c => c.Category == category);

            return await query.ToListAsync();
        }

        public async Task<EducationalContent?> GetContentById(int id)
        {
            return await _context.EducationalContents.FindAsync(id);
        }
    }
}