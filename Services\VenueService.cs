using System.Collections.Generic;
using System.Threading.Tasks;
using AgriEnergyPlatform.Models;
using AgriEnergyPlatform.Data;
using Microsoft.EntityFrameworkCore;

namespace AgriEnergyPlatform.Services
{
    public class VenueService : IVenueService
    {
        private readonly ApplicationDbContext _context;
        public VenueService(ApplicationDbContext context)
        {
            _context = context;
        }
        public async Task<List<Venue>> GetVenuesAsync()
        {
            return await _context.Venues.ToListAsync();
        }
        public async Task<Venue?> GetVenueByIdAsync(int id)
        {
            return await _context.Venues.FindAsync(id);
        }
        public async Task AddVenueAsync(Venue venue)
        {
            _context.Venues.Add(venue);
            await _context.SaveChangesAsync();
        }
        public async Task UpdateVenueAsync(Venue venue)
        {
            _context.Venues.Update(venue);
            await _context.SaveChangesAsync();
        }
        public async Task DeleteVenueAsync(int id)
        {
            var venue = await _context.Venues.FindAsync(id);
            if (venue != null)
            {
                _context.Venues.Remove(venue);
                await _context.SaveChangesAsync();
            }
        }
    }
}
