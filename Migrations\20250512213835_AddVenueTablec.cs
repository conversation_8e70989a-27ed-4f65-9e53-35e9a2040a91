﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AgriEnergyPlatform.Migrations
{
    /// <inheritdoc />
    public partial class AddVenueTablec : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AdditionalInfo",
                table: "TrainingEvents",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "TrainingEvents",
                type: "TEXT",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "OrganizerId",
                table: "TrainingEvents",
                type: "TEXT",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "OrganizerType",
                table: "TrainingEvents",
                type: "TEXT",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "VenueId",
                table: "TrainingEvents",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "PaymentDate",
                table: "TrainingEnrollments",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PaymentStatus",
                table: "TrainingEnrollments",
                type: "TEXT",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "Venues",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Name = table.Column<string>(type: "TEXT", nullable: false),
                    Address = table.Column<string>(type: "TEXT", nullable: false),
                    IsFree = table.Column<bool>(type: "INTEGER", nullable: false),
                    ContactInfo = table.Column<string>(type: "TEXT", nullable: true),
                    Notes = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Venues", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_TrainingEvents_VenueId",
                table: "TrainingEvents",
                column: "VenueId");

            migrationBuilder.AddForeignKey(
                name: "FK_TrainingEvents_Venues_VenueId",
                table: "TrainingEvents",
                column: "VenueId",
                principalTable: "Venues",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TrainingEvents_Venues_VenueId",
                table: "TrainingEvents");

            migrationBuilder.DropTable(
                name: "Venues");

            migrationBuilder.DropIndex(
                name: "IX_TrainingEvents_VenueId",
                table: "TrainingEvents");

            migrationBuilder.DropColumn(
                name: "AdditionalInfo",
                table: "TrainingEvents");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "TrainingEvents");

            migrationBuilder.DropColumn(
                name: "OrganizerId",
                table: "TrainingEvents");

            migrationBuilder.DropColumn(
                name: "OrganizerType",
                table: "TrainingEvents");

            migrationBuilder.DropColumn(
                name: "VenueId",
                table: "TrainingEvents");

            migrationBuilder.DropColumn(
                name: "PaymentDate",
                table: "TrainingEnrollments");

            migrationBuilder.DropColumn(
                name: "PaymentStatus",
                table: "TrainingEnrollments");
        }
    }
}
