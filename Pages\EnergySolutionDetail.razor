@page "/energy-solutions/{Slug}"
@attribute [Authorize(Roles = "Admin,User")]
@using AgriEnergyPlatform.Models
@using AgriEnergyPlatform.Services
@inject IEnergyProductService ProductService
@inject NavigationManager NavigationManager

<div class="container my-5">
    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    }
    else if (product == null)
    {
        <div class="alert alert-warning">Product not found or an error occurred. @errorMessage</div>
   
    }
    else
    {
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-lg">
                    <div class="card-header bg-success text-white">
                        <h1 class="mb-0">@product.Name</h1>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-5 text-center">
                                <img src="@product.ImageUrl" 
                                     alt="@product.Name" 
                                     class="img-fluid rounded mb-3 shadow-sm"
                                     style="max-height: 350px; object-fit: contain; border: 1px solid #eee;"
                                     onerror="this.src='/images/energy-placeholder.svg'">
                            </div>
                            <div class="col-md-7">
                                <p class="lead">@product.Description</p>
                                <hr/>
                                <p><strong>Price:</strong> <span class="fw-bold text-success fs-5">@product.Price.ToString("C")</span></p>
                                <p><strong>Category:</strong> <span class="badge bg-info text-dark">@product.Category</span></p>
                                @if (!string.IsNullOrEmpty(product.Manufacturer))
                                {
                                    <p><strong>Manufacturer:</strong> @product.Manufacturer</p>
                                }
                                <p><strong>Availability:</strong> 
                                    <span class="badge @(product.IsAvailable ? "bg-success" : "bg-danger")">
                                        @(product.IsAvailable ? "Available" : "Unavailable")
                                    </span>
                                </p>
                                @if (product.KeyFeatures != null && product.KeyFeatures.Any())
                                {
                                    <h5 class="mt-4">Key Features:</h5>
                                    <ul class="list-unstyled">
                                        @foreach (var feature in product.KeyFeatures)
                                        {
                                            <li><i class="fas fa-check-circle text-success me-2"></i>@feature</li>
                                        }
                                    </ul>
                                }
                                @if (!string.IsNullOrEmpty(product.Specifications))
                                {
                                    <h5 class="mt-4">Specifications:</h5>
                                    <p>@product.Specifications</p>
                                }
                            </div>
                        </div>

                        @if (product.Vendor != null)
                        {
                            <hr class="my-4"/>
                            <div class="vendor-details bg-light p-4 rounded">
                                <h4 class="mb-3">Vendor Information</h4>
                                <p><strong>Company:</strong> @product.Vendor.CompanyName</p>
                                @if (!string.IsNullOrEmpty(product.Vendor.ContactEmail))
                                {
                                    <p><strong>Email:</strong> <a href="mailto:@product.Vendor.ContactEmail">@product.Vendor.ContactEmail</a></p>
                                }
                                @if (!string.IsNullOrEmpty(product.Vendor.ContactPhone))
                                {
                                    <p><strong>Phone:</strong> <a href="tel:@product.Vendor.ContactPhone">@product.Vendor.ContactPhone</a></p>
                                }
                                @if (!string.IsNullOrEmpty(product.Vendor.Website))
                                {
                                    <p><strong>Website:</strong> <a href="@product.Vendor.Website" target="_blank">@product.Vendor.Website</a></p>
                                }
                            </div>
                        }
                    </div>
                    <div class="card-footer text-center">
                 
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    [Parameter]
    public string? Slug { get; set; }

    private EnergyProduct? product;
    private bool isLoading = true;
    private string? errorMessage;

    protected override async Task OnInitializedAsync()
    {
        if (string.IsNullOrWhiteSpace(Slug))
        {
            errorMessage = "Product identifier is missing.";
            isLoading = false;
            return;
        }

        try
        {
            // Assuming GetProductBySlugAsync includes Vendor details
            product = await ProductService.GetProductBySlugAsync(Slug);
            if (product == null)
            {
                errorMessage = $"Product with slug '{Slug}' not found.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading product details: {ex.Message}";
            // Log the exception ex
        }
        finally
        {
            isLoading = false;
        }
    }
}

<style>
    .card-header h1 {
        font-size: 1.75rem;
    }
    .lead {
        font-size: 1.1rem;
        color: #555;
    }
    .vendor-details {
        border: 1px solid #e0e0e0;
    }
</style>