using System.ComponentModel.DataAnnotations;

namespace AgriEnergyPlatform.Models
{
    public class EnergySolution
    {
        public int Id { get; set; }
        [Required]
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string ImageUrl { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public List<SolutionFeedback> Feedbacks { get; set; } = new();
    }
}