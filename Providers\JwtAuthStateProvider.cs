using Microsoft.AspNetCore.Components.Authorization;
using System.Security.Claims;
using Microsoft.JSInterop;
using AgriEnergyPlatform.Services;
using Microsoft.AspNetCore.Http;

namespace AgriEnergyPlatform.Services // <-- Add this line
{
    public class JwtAuthStateProvider : AuthenticationStateProvider
    {
        private readonly IJSRuntime _js;
        private readonly IAuthService _authService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public JwtAuthStateProvider(
            IJSRuntime js, 
            IAuthService authService,
            IHttpContextAccessor httpContextAccessor)
        {
            _js = js;
            _authService = authService;
            _httpContextAccessor = httpContextAccessor;
        }

        public override async Task<AuthenticationState> GetAuthenticationStateAsync()
        {
            // During prerendering, use the HTTP context
            if (_httpContextAccessor.HttpContext?.Request.Path == "/_host")
            {
                var user = _httpContextAccessor.HttpContext.User;
                return new AuthenticationState(user ?? new ClaimsPrincipal(new ClaimsIdentity()));
            }

            // Client-side execution
            try
            {
                var token = await _js.InvokeAsync<string>("sessionStorage.getItem", "authToken");
                ClaimsPrincipal principal = new ClaimsPrincipal(new ClaimsIdentity());

                if (!string.IsNullOrEmpty(token))
                {
                    var validatedPrincipal = _authService.ValidateJwtToken(token);
                    if (validatedPrincipal != null)
                        principal = validatedPrincipal;
                }

                return new AuthenticationState(principal);
            }
            catch
            {
                return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
            }
        }

        public async Task MarkUserAsAuthenticated(string token)
        {
            var principal = await Task.Run(() => _authService.ValidateJwtToken(token)) 
                           ?? new ClaimsPrincipal(new ClaimsIdentity());
            NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(principal)));
        }

        public async Task MarkUserAsLoggedOut()
        {
            try 
            {
                await _js.InvokeVoidAsync("sessionStorage.removeItem", "authToken");
                NotifyAuthenticationStateChanged(Task.FromResult(
                    new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()))));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during logout: {ex.Message}");
            }
        }
    }
}