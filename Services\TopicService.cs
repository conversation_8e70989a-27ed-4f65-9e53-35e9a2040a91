using AgriEnergyPlatform.Data;
using AgriEnergyPlatform.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
namespace AgriEnergyPlatform.Services
{
    public class TopicService
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public TopicService(ApplicationDbContext context, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public async Task<List<ForumTopic>> GetPublishedTopicsAsync()
        {
            return await _context.ForumTopics
                .Include(t => t.User)
                .Include(t => t.Comments)
                .ThenInclude(c => c.User)
                .Where(t => t.IsPublished)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<ForumTopic>> GetUnpublishedTopicsAsync()
        {
            return await _context.ForumTopics
                .Include(t => t.User)
                .Where(t => !t.IsPublished)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();
        }

        public async Task<ForumTopic> GetTopicByIdAsync(int id)
        {
            // return await _context.ForumTopics.FindAsync(id) ?? new ForumTopic();
            return await _context.ForumTopics.Include(t => t.User)
                .Include(t => t.Comments)
                    .ThenInclude(c => c.User)
                .FirstOrDefaultAsync(t => t.Id == id);
        }

        public async Task<int> GetLikesCountAsync(int topicId)
        {
            return await _context.TopicLikes
                .CountAsync(tl => tl.ForumTopicId == topicId);
        }

        public async Task LikeTopicAsync(int topicId, string userId)
        {
            var existingLike = await _context.TopicLikes
                .FirstOrDefaultAsync(tl => tl.ForumTopicId == topicId && tl.UserId == userId);

            if (existingLike == null)
            {
                _context.TopicLikes.Add(new TopicLike
                {
                    ForumTopicId = topicId,
                    UserId = userId,
                    CreatedAt = DateTime.UtcNow
                });
                await _context.SaveChangesAsync();
            }
        }

        public async Task AddTopicAsync(ForumTopic topic, string userId)
        {
            topic.UserId = userId;
            _context.ForumTopics.Add(topic);
            await _context.SaveChangesAsync();
        }

        public async Task PublishTopicAsync(int topicId)
        {
            var topic = await _context.Topics.FindAsync(topicId);
            if (topic != null)
            {
                topic.IsPublished = true;
                await _context.SaveChangesAsync();
            }
        }

        public async Task AddCommentAsync(Comment comment, string userId, int topicId)
        {
            var topicExists = await _context.ForumTopics.AnyAsync(t => t.Id == topicId);
            var userExists = await _context.Users.AnyAsync(u => u.Id == userId);
            if (!topicExists || !userExists)
            {
                Console.WriteLine($"[DEBUG] Topic or user does not exist. topicExists: {topicExists}, userExists: {userExists}");
                return;
            }
            comment.UserId = userId;
            comment.ForumTopicId = topicId;
            comment.CreatedAt = DateTime.UtcNow;
            _context.Comments.Add(comment);
            await _context.SaveChangesAsync();
        }
    }
}