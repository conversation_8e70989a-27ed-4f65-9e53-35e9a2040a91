using System.ComponentModel.DataAnnotations;

namespace AgriEnergyPlatform.Models
{
    public class EnergyProduct
    {
        [Key]
        public int Id { get; set; }
        [Required]
        public string Name { get; set; } = string.Empty;
        [Required]
        public string Description { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public string Category { get; set; } = string.Empty;
        public string Manufacturer { get; set; } = string.Empty;
        public string Specifications { get; set; } = string.Empty;
        public string ImageUrl { get; set; } = string.Empty;
        public bool IsAvailable { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? LastUpdated { get; set; }
        public List<string> KeyFeatures { get; set; } = new();
        public string Slug { get; set; } = string.Empty; // Ensure non-null value

        // Foreign key for Vendor
        public int? VendorId { get; set; }
        // Navigation property
        public Vendor? Vendor { get; set; }
    }
}