@page "/login"
@using AgriEnergyPlatform.Services
@using System.ComponentModel.DataAnnotations
@inject IAuthService AuthService
@inject AuthenticationStateProvider AuthStateProvider
@inject IJSRuntime JS
@inject NavigationManager NavigationManager
@inject JwtAuthStateProvider JwtAuthStateProvider

<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center align-items-center" style="min-height: 100vh;">
            <div class="col-lg-10">
                <div class="card auth-card shadow-lg">
                    <div class="row g-0">
                        <!-- Left side with image -->
                        <div class="col-md-6 d-none d-md-block">
                            <div class="auth-image h-100">
                                <img src="images/5.jpg" 
                                     alt="Sustainable farming" 
                                     class="img-fluid h-100 w-100 object-fit-cover rounded-start">
                                <div class="image-overlay p-4">
                                    <h2 class="text-white">Welcome Back</h2>
                                    <p class="text-white-50">Collaborating for a sustainable agricultural future</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Right side with login form -->
                        <div class="col-md-6">
                            <div class="card-body p-4 p-md-5">
                                <div class="text-center mb-4">
                                    <i class="fas fa-leaf text-primary mb-3" style="font-size: 2.5rem;"></i>
                                    <h2>Sign In</h2>
                                    <p class="text-muted">Enter your credentials to access your account</p>
                                </div>
                                
                                @if (!string.IsNullOrEmpty(error))
                                {
                                    <div class="alert alert-danger d-flex align-items-center">
                                        <i class="fas fa-exclamation-circle me-2"></i>
                                        <div>@error</div>
                                    </div>
                                }

                                <EditForm Model="loginModel" OnValidSubmit="HandleLogin">
                                    <DataAnnotationsValidator />

                                    <div class="mb-4">
                                        <label class="form-label">Email Address</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light">
                                                <i class="fas fa-envelope text-muted"></i>
                                            </span>
                                            <InputText @bind-Value="loginModel.Email" class="form-control" placeholder="Enter your email" />
                                        </div>
                                        <ValidationMessage For="@(() => loginModel.Email)" class="text-danger small" />
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Password</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light">
                                                <i class="fas fa-lock text-muted"></i>
                                            </span>
                                            <InputText type="password" @bind-Value="loginModel.Password" class="form-control" placeholder="Enter your password" />
                                        </div>
                                        <ValidationMessage For="@(() => loginModel.Password)" class="text-danger small" />
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center mb-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="rememberMe">
                                            <label class="form-check-label" for="rememberMe">
                                                Remember me
                                            </label>
                                        </div>
                                        <a href="/forgot-password" class="text-decoration-none">Forgot password?</a>
                                    </div>

                                    <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
                                        <i class="fas fa-sign-in-alt me-2"></i> Login
                                    </button>
                                    
                                    <div class="text-center mt-4">
                                        <p class="text-muted">Don't have an account? 
                                            <a href="/register" class="text-decoration-none fw-bold">Create one</a>
                                        </p>
                                    </div>
                                </EditForm>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .auth-container {
        background-color: #f8f9fa;
        min-height: 100vh;
    }
    
    .auth-card {
        border: none;
        border-radius: 1rem;
        overflow: hidden;
    }
    
    .auth-image {
        position: relative;
    }
    
    .image-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0,0,0,0.7));
    }
    
    .form-control:focus {
        border-color: #2e7d32;
        box-shadow: 0 0 0 0.25rem rgba(46, 125, 50, 0.25);
    }
    
    .btn-primary {
        background-color: #2e7d32;
        border-color: #2e7d32;
    }
    
    .btn-primary:hover {
        background-color: #1b5e20;
        border-color: #1b5e20;
    }
</style>

@code {
    private LoginModel loginModel = new();
    private string error = string.Empty;

    private async Task HandleLogin()
    {
        error = string.Empty;
        var (user, token) = await AuthService.Login(loginModel.Email, loginModel.Password);
        
        if (user == null || string.IsNullOrEmpty(token))
        {
            error = "Invalid login attempt";
            return;
        }
    
        await JS.InvokeVoidAsync("sessionStorage.setItem", "authToken", token);
        await JwtAuthStateProvider.MarkUserAsAuthenticated(token); 
        NavigationManager.NavigateTo("/",forceLoad: true);
    }

    public class LoginModel
    {
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string Email { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Password is required")]
        [MinLength(6, ErrorMessage = "Password must be at least 6 characters")]
        public string Password { get; set; } = string.Empty;
    }
}