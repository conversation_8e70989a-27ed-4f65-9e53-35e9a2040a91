using AgriEnergyPlatform.Data;
using AgriEnergyPlatform.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AgriEnergyPlatform.Services
{
    public class ExpertMessageService : IExpertMessageService
    {
        private readonly ApplicationDbContext _context;
        public ExpertMessageService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task SendMessageAsync(int expertId, string senderUserId, string message)
        {
            var msg = new ExpertMessage
            {
                ExpertId = expertId,
                SenderUserId = senderUserId,
                Message = message
            };
            _context.ExpertMessages.Add(msg);
            await _context.SaveChangesAsync();
        }

        public async Task<List<ExpertMessage>> GetMessagesForExpertAsync(int expertId)
        {
            return await _context.ExpertMessages
                .Where(m => m.ExpertId == expertId)
                .OrderByDescending(m => m.SentAt)
                .ToListAsync();
        }

        public async Task<int> GetUnreadCountForExpertAsync(int expertId)
        {
            return await _context.ExpertMessages
                .CountAsync(m => m.ExpertId == expertId && !m.IsRead);
        }

        public async Task MarkMessagesAsReadAsync(int expertId)
        {
            var unread = await _context.ExpertMessages
                .Where(m => m.ExpertId == expertId && !m.IsRead)
                .ToListAsync();
            foreach (var msg in unread)
                msg.IsRead = true;
            await _context.SaveChangesAsync();
        }
    }
}
