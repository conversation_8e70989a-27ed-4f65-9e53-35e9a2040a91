@page "/admin/dashboard"
@attribute [Authorize(Roles = "Admin")]
@using AgriEnergyPlatform.Models
@using AgriEnergyPlatform.Services
@inject ITrainingService TrainingService
@inject IVenueService VenueService

<h2>Admin Dashboard</h2>

@if (isLoading)
{
    <div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>
}
else
{
    <h4>All Trainings</h4>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Title</th>
                <th>Date</th>
                <th>Organizer</th>
                <th>Type</th>
                <th>Venue</th>
                <th>Registrations</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var training in trainings)
            {
                var venue = training.VenueId != null ? venues.FirstOrDefault(v => v.Id == training.VenueId) : null;
                <tr>
                    <td>@training.Title</td>
                    <td>@training.Date.ToShortDateString()</td>
                    <td>@training.OrganizerType @training.OrganizerId</td>
                    <td>@(training.IsOnline ? "Online" : "Physical")</td>
                    <td>@venue?.Name</td>
                    <td>@enrollments.Count(e => e.TrainingId == training.Id)</td>
                    <td>
                        <a class="btn btn-sm btn-outline-primary" href="/training/@training.Id">Details</a>
                    </td>
                </tr>
            }
        </tbody>
    </table>
    <h4 class="mt-5">All Venues</h4>
    <a class="btn btn-success mb-2" href="/admin/venues">Manage Venues</a>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Name</th>
                <th>Address</th>
                <th>Is Free</th>
                <th>Contact</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var venue in venues)
            {
                <tr>
                    <td>@venue.Name</td>
                    <td>@venue.Address</td>
                    <td>@(venue.IsFree ? "Yes" : "No")</td>
                    <td>@venue.ContactInfo</td>
                </tr>
            }
        </tbody>
    </table>
    <h4 class="mt-5">All Registrations</h4>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>User</th>
                <th>Training</th>
                <th>Date</th>
                <th>Payment Status</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var enroll in enrollments)
            {
                var training = trainings.FirstOrDefault(t => t.Id == enroll.TrainingId);
                <tr>
                    <td>@enroll.UserId</td>
                    <td>@training?.Title</td>
                    <td>@enroll.EnrollmentDate.ToShortDateString()</td>
                    <td>@enroll.PaymentStatus</td>
                </tr>
            }
        </tbody>
    </table>
}

@code {
    private List<TrainingEvent> trainings = new();
    private List<Venue> venues = new();
    private List<TrainingEnrollment> enrollments = new();
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        isLoading = true;
        trainings = await TrainingService.GetUpcomingEventsAsync();
        venues = await VenueService.GetVenuesAsync();
        // Get all enrollments (for demo, get all; in production, use a paged approach)
        enrollments = new List<TrainingEnrollment>();
        foreach (var t in trainings)
        {
            var regs = await TrainingService.GetRegistrationsForEventAsync(t.Id);
            enrollments.AddRange(regs);
        }
        isLoading = false;
    }
}
