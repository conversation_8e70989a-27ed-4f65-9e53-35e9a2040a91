@using System.Security.Claims
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager NavigationManager
@inject IJSRuntime JS

<nav class="navbar navbar-expand-lg navbar-dark bg-primary-gradient shadow-sm">
    <div class="container">
        <!-- Brand with smooth hover effect -->
        <a class="navbar-brand px-2 d-flex align-items-center" href="/">
           <span class="fw-bold">FarmConnect</span>
        </a>

        <!-- Mobile toggle button -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation items -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                @if (isAuthenticated)
                {
                    <li class="nav-item">
                        <NavLink class="nav-link px-3" href="energy-solutions">
                            <i class="fas fa-bolt me-1"></i> Energy
                        </NavLink>
                    </li>
                    <li class="nav-item">
                        <NavLink class="nav-link px-3" href="expertise">
                            <i class="fas fa-user-tie me-1"></i> Experts
                        </NavLink>
                    </li>
                    <li class="nav-item">
                        <NavLink class="nav-link px-3" href="training">
                            <i class="fas fa-graduation-cap me-1"></i> Training
                        </NavLink>
                    </li>
                    <li class="nav-item">
                        <NavLink class="nav-link px-3" href="insights">
                            <i class="fas fa-chart-line me-1"></i> Insights
                        </NavLink>
                    </li>
                }
            </ul>

            <!-- Auth section -->
            <div class="d-flex align-items-center ms-lg-3">
                @if (!isAuthenticated)
                {
                    <a href="login" class="btn btn-outline-light me-2 px-3">
                        <i class="fas fa-sign-in-alt me-1"></i> Login
                    </a>
                    <a href="register" class="btn btn-light text-primary px-3">
                        <i class="fas fa-user-plus me-1"></i> Register
                    </a>
                }
                else
                {
                    <!-- User dropdown -->
                   <div class="d-flex flex-row">
                        <button class="btn btn-transparent dropdown-toggle d-flex align-items-center" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="avatar-circle bg-white text-primary fw-bold me-2">
                                @GetInitials(userName)
                            </div>
                            <span class="d-none d-lg-inline text-white">@userName</span>
                        </button>
                        <button class="dropdown-item text-danger" @onclick="HandleLogout">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </button>
                     
                    </div>
                }
            </div>
        </div>
    </div>
</nav>

@implements IDisposable

@code {
    private bool isAuthenticated = false;
    private string? userName;

    protected override async Task OnInitializedAsync()
    {
        await UpdateAuthState();
        AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
    }

    private async void OnAuthenticationStateChanged(Task<AuthenticationState> task)
    {
        await UpdateAuthState();
        StateHasChanged();
    }

    private async Task UpdateAuthState()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        isAuthenticated = user.Identity?.IsAuthenticated ?? false;
        userName = user.FindFirst(c => c.Type == "name")?.Value ?? 
                  user.FindFirst(c => c.Type == ClaimTypes.Name)?.Value;
    }

    private async Task HandleLogout()
    {
        await JS.InvokeVoidAsync("sessionStorage.removeItem", "authToken");
        await JS.InvokeVoidAsync("localStorage.removeItem", "authToken");
        await AuthenticationStateProvider.GetAuthenticationStateAsync();
        NavigationManager.NavigateTo("/", forceLoad: true);
    }

    private string GetInitials(string? fullName)
    {
        if (string.IsNullOrEmpty(fullName)) return "?";
        var names = fullName.Split(' ');
        var initials = names.Length switch
        {
            1 => names[0][0].ToString(),
            _ => $"{names[0][0]}{names[^1][0]}"
        };
        return initials.ToUpper();
    }

    public void Dispose()
    {
        AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
    }
}

<style>
    /* Modern gradient background */
    .bg-primary-gradient {
        background: linear-gradient(135deg, #2b5876 0%, #4e4376 100%);
    }

    /* Smooth nav item transitions */
    .nav-link {
        transition: all 0.3s ease;
        border-radius: 4px;
        margin: 0 2px;
    }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .nav-link.active {
            font-weight: 600;
            background: rgba(255, 255, 255, 0.2);
        }

    /* User avatar circle */
    .avatar-circle {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
    }

    /* Dropdown styling */
    .dropdown-menu {
        border: none;
        min-width: 200px;
    }

    .dropdown-item {
        padding: 0.5rem 1rem;
        transition: all 0.2s;
    }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            padding-left: 1.25rem;
        }

    /* Button hover effects */
    .btn-outline-light:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(255, 255, 255, 0.2);
    }

    .btn-light:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* Mobile menu adjustments */
    @@media (max-width: 992px) {
        .navbar-collapse {
            padding: 1rem 0;
        }

        .nav-link {
            padding: 0.75rem 1rem;
            margin: 2px 0;
        }

        .d-flex {
            margin-top: 1rem;
            flex-direction: column;
            gap: 0.5rem;
            width: 100%;
        }

            .d-flex a, .d-flex button {
                width: 100%;
                text-align: center;
            }
    }
</style>