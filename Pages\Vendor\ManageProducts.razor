@page "/vendor/manage-products"
@attribute [Authorize(Roles = "Vendor")]
@using AgriEnergyPlatform.Models
@using AgriEnergyPlatform.Services
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Identity
@inject IEnergyProductService ProductService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserManager<ApplicationUser> UserManager
@inject NavigationManager NavigationManager
@inject IVendorService VendorService

<div class="container my-5">
    <div class="section-title">
        <h1>Manage Your Energy Products</h1>
        <p class="text-muted">Add new energy solutions to the platform.</p>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    }
    else
    {
        <div class="container-fluid mt-4">
            <div class="row">
        
                <div class="col-lg-6 pe-lg-4">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">Add New Product</h5>
                        </div>
                        <div class="card-body">
                            <EditForm Model="@newProduct" OnValidSubmit="HandleAddProductAsync">
                                <DataAnnotationsValidator />
                                <ValidationSummary />

                                <div class="mb-3">
                                    <label for="productName" class="form-label">Product Name</label>
                                    <InputText id="productName" class="form-control" @bind-Value="newProduct.Name" />
                                </div>

                                <div class="mb-3">
                                    <label for="productDescription" class="form-label">Description</label>
                                    <InputTextArea id="productDescription" class="form-control" @bind-Value="newProduct.Description" rows="3" />
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="productPrice" class="form-label">Price</label>
                                        <InputNumber id="productPrice" class="form-control" @bind-Value="newProduct.Price" />
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="productCategory" class="form-label">Category</label>
                                        <InputText id="productCategory" class="form-control" @bind-Value="newProduct.Category" />
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="productManufacturer" class="form-label">Manufacturer (Optional)</label>
                                    <InputText id="productManufacturer" class="form-control" @bind-Value="newProduct.Manufacturer" />
                                </div>

                                <div class="mb-3">
                                    <label for="productImageUrl" class="form-label">Image URL (Optional)</label>
                                    <InputText id="productImageUrl" class="form-control" @bind-Value="newProduct.ImageUrl" />
                                </div>

                                <div class="mb-3">
                                    <label for="productSpecifications" class="form-label">Specifications (Optional, comma-separated)</label>
                                    <InputText id="productSpecifications" class="form-control" @bind-Value="newProduct.Specifications" />
                                </div>

                                <div class="mb-3">
                                    <label for="keyFeatures" class="form-label">Key Features (Optional, comma-separated)</label>
                                    <InputText id="keyFeatures" class="form-control" @bind-Value="keyFeaturesInput" />
                                </div>

                                <div class="form-check mb-3">
                                    <InputCheckbox id="isAvailable" class="form-check-input" @bind-Value="newProduct.IsAvailable" />
                                    <label class="form-check-label" for="isAvailable">Is Available</label>
                                </div>

                                @if (!string.IsNullOrEmpty(errorMessage))
                                {
                                    <div class="alert alert-danger">@errorMessage</div>
                                }
                                @if (!string.IsNullOrEmpty(successMessage))
                                {
                                    <div class="alert alert-success">@successMessage</div>
                                }

                                <button type="submit" class="btn btn-success" disabled="@isSubmitting">
                                    @if (isSubmitting)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                        <span>Adding...</span>
                                    }
                                    else
                                    {
                                        <i class="fas fa-plus-circle me-2"></i>
                                        <span>Add Product</span>
                                    }
                                </button>
                            </EditForm>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 ps-lg-4">
                            <div class="card shadow-sm">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Your Products</h5>
                                </div>
                                <div class="card-body">
                                    @if (vendorProducts == null || !vendorProducts.Any())
                                    {
                                        <div class="alert alert-info">You have not added any products yet.</div>
                                    }
                                    else
                                    {
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>Name</th>
                                                        <th>Category</th>
                                                        <th>Price</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach (var product in vendorProducts)
                                                    {
                                                        <tr class="cursor-pointer" @onclick="() => ViewProductDetails(product)">
                                                            <td>
                                                                <strong>@product.Name</strong>
                                                                <div class="text-muted small">@(product.Description.Length > 50 ? product.Description.Substring(0, 50) + "..." : product.Description)</div>
                                                            </td>
                                                            <td>@product.Category</td>
                                                            <td>@product.Price.ToString("C")</td>
                                                            <td>
                                                                <span class="badge bg-@(product.IsAvailable ? "success" : "secondary")">
                                                                    @(product.IsAvailable ? "Available" : "Unavailable")
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

@code {
    private bool isLoading = true;
    private bool isVendor = false;

    private void ViewProductDetails(EnergyProduct product)
    {
        if (!string.IsNullOrEmpty(product.Slug))
        {
            NavigationManager.NavigateTo($"/energy-solutions/{product.Slug}");
        }
    }
    private EnergyProduct newProduct = new EnergyProduct { IsAvailable = true };
    private string keyFeaturesInput = string.Empty;
    private string? errorMessage;
    private string? successMessage;
    private bool isSubmitting = false;
    private string? currentUserId;
    private Vendor? currentVendor;
    private List<EnergyProduct>? vendorProducts;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity != null && user.Identity.IsAuthenticated)
        {
            currentUserId = UserManager.GetUserId(user);
            if (!string.IsNullOrEmpty(currentUserId))
            {
                currentVendor = await VendorService.GetVendorByApplicationUserIdAsync(currentUserId);
                if (currentVendor == null)
                {
                    errorMessage = "Vendor profile not found. Please ensure your vendor account is set up correctly.";
                    // Optionally, redirect or show a more prominent error.
                }
                else
                {
                    await LoadVendorProducts();
                }
            }
        }
        isLoading = false;
    }

    private async Task LoadVendorProducts()
    {
        if (!string.IsNullOrEmpty(currentUserId))
        {
            // Fetch products specifically for the current vendor if currentVendor and its Id are available.
            if (currentVendor != null)
            {
                var allProducts = await ProductService.GetProducts(); // Gets all products, includes Vendor info
                // Filter products by the Vendor's primary key (Id)
                vendorProducts = allProducts.Where(p => p.VendorId == currentVendor.Id).ToList();
            }
            else
            {
                vendorProducts = new List<EnergyProduct>(); // Initialize to empty if no vendor found
            }
        }
    }

    private async Task HandleAddProductAsync()
    {
        errorMessage = null;
        successMessage = null;
        isSubmitting = true;

        if (currentVendor == null)
        {
            errorMessage = "Vendor profile not found. Cannot add product. Please ensure your vendor account is correctly set up.";
            isSubmitting = false;
            return;
        }

        newProduct.VendorId = currentVendor.Id; // Assign the Vendor's primary key

        if (!string.IsNullOrWhiteSpace(keyFeaturesInput))
        {
            newProduct.KeyFeatures = keyFeaturesInput.Split(',').Select(s => s.Trim()).ToList();
        }
        else
        {
            newProduct.KeyFeatures = new List<string>();
        }
        
        newProduct.Slug = GenerateSlug(newProduct.Name); // Simple slug generation

        try
        {
            var createdProduct = await ProductService.CreateProductAsync(newProduct);
            successMessage = $"Product '{createdProduct.Name}' added successfully!";
            newProduct = new EnergyProduct { IsAvailable = true }; // Reset form
            keyFeaturesInput = string.Empty;
            await LoadVendorProducts(); // Refresh the list
        }
        catch (Exception ex)
        {   
            // Log the full exception for debugging: Console.WriteLine(ex.ToString());
            errorMessage = $"Error adding product: {ex.Message}. Ensure your vendor profile is correctly set up and try again. You might need to set the VendorId for the product.";
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private string GenerateSlug(string name)
    {
        if (string.IsNullOrWhiteSpace(name)) return string.Empty;
        var slug = name.ToLowerInvariant().Replace(" ", "-");
        // Remove invalid characters (simplified)
        slug = System.Text.RegularExpressions.Regex.Replace(slug, @"[^a-z0-9-]", "");
        return slug;
    }
}