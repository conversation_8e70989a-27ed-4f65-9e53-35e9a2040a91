@using AgriEnergyPlatform.Models;
@inject ApplicationDbContext _context

<h2>Feedback</h2>
<EditForm Model="feedbackModel" OnValidSubmit="HandleValidSubmit">

    <DataAnnotationsValidator />
    <ValidationSummary />
    <div class="mb-3">
        <label for="comment">Comment</label>
        <InputTextArea id="comment" class="form-control" @bind-Value="feedbackModel.Comment" />
    </div>
    <div class="mb-3">
        <label for="rating">Rating</label>
        <InputNumber id="rating" class="form-control" @bind-Value="feedbackModel.Rating" />
    </div>
    <button type="submit" class="btn btn-primary">Submit Feedback</button>
</EditForm>

@code {
    public class FeedbackModel
    {
        public string Comment { get; set; } = string.Empty;
        public int Rating { get; set; }
    }

    @using AgriEnergyPlatform.Data

    private FeedbackModel feedbackModel = new FeedbackModel();

    private async Task HandleValidSubmit()  
    {
        var feedback = new SolutionFeedback {
                Comment = feedbackModel.Comment,
                Rating = feedbackModel.Rating,
                CreatedAt = DateTime.UtcNow
            };

            await _context.SolutionFeedbacks.AddAsync(feedback);
            await _context.SaveChangesAsync();
            feedbackModel = new FeedbackModel();
    }

}
