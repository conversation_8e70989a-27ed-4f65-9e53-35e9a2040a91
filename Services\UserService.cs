using AgriEnergyPlatform.Data;
using AgriEnergyPlatform.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AgriEnergyPlatform.Services
{
    public class UserService : IUserService
    {
        private readonly ApplicationDbContext _context;
        public UserService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<ApplicationUser>> GetAllUsersAsync()
        {
            return await _context.Users.ToListAsync();
        }

        public async Task<ApplicationUser?> GetUserByIdAsync(string id)
        {
            return await _context.Users.FindAsync(id);
        }

        public async Task PromoteToExpertAsync(ApplicationUser user, string? bio, string? specialization, string? contactInfo)
        {
            var existingExpert = await _context.Experts.FirstOrDefaultAsync(e => e.UserId == user.Id);
            if (existingExpert == null)
            {
                Console.WriteLine($"[PromoteToExpertAsync] Adding user {user.UserName} to Experts table.");
                _context.Experts.Add(new Expert
                {
                    UserId = user.Id,
                    Name = user.Name ?? user.FullName ?? user.UserName,
                    Bio = bio,
                    Specialization = specialization,
                    ContactInfo = contactInfo
                });
            }
            else
            {
                Console.WriteLine($"[PromoteToExpertAsync] Updating expert info for user {user.UserName}.");
                existingExpert.Name = user.Name ?? user.FullName ?? user.UserName;
                existingExpert.Bio = bio;
                existingExpert.Specialization = specialization;
                existingExpert.ContactInfo = contactInfo;
                _context.Experts.Update(existingExpert);
            }

            if (user.Role != "Expert")
            {
                Console.WriteLine($"[PromoteToExpertAsync] Updating user {user.UserName} role to Expert.");
                user.Role = "Expert";
                _context.Users.Update(user);
            }
            await _context.SaveChangesAsync();
        }
    }
}
