using Microsoft.EntityFrameworkCore;
using AgriEnergyPlatform.Data;
using AgriEnergyPlatform.Services;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using AgriEnergyPlatform.Models;


var builder = WebApplication.CreateBuilder(args);
builder.Services.AddSingleton<AgriEnergyPlatform.Services.GmailEmailService>();
builder.WebHost.UseUrls("https://localhost:7128", "http://localhost:5128");
// Add services to the container
builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor(options => {
    options.DetailedErrors = true;
    options.DisconnectedCircuitRetentionPeriod = TimeSpan.FromMinutes(2);
});
builder.Services.AddHttpClient();

// Configure SQLite
builder.Services.AddDbContext<ApplicationDbContext>(options =>{
    options.UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection"));
    });



// Register Services
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IExpertService, ExpertService>();
builder.Services.AddScoped<IEnergyProductService, EnergyProductService>();
builder.Services.AddScoped<IHomePageService, HomePageService>();
builder.Services.AddScoped<IEducationalContentService, EducationalContentService>();
builder.Services.AddScoped<IForumService, ForumService>();
builder.Services.AddScoped<IExpertiseService, ExpertiseService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IExpertMessageService, ExpertMessageService>();
builder.Services.AddScoped<IVendorService, VendorService>();
builder.Services.AddScoped<IVenueService, VenueService>();
builder.Services.AddScoped<TopicService>();


// Ensure you have:
builder.Services.AddScoped<AuthenticationStateProvider, JwtAuthStateProvider>();
builder.Services.AddScoped<JwtAuthStateProvider>();

builder.Services.AddIdentity<ApplicationUser, IdentityRole>()
    .AddEntityFrameworkStores<ApplicationDbContext>()
    .AddDefaultTokenProviders();

builder.Services.AddScoped<ITrainingService, TrainingService>();
builder.Services.AddScoped<TopicService>();
var app = builder.Build();

// Configure the HTTP request pipeline
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();

app.MapBlazorHub();
app.MapFallbackToPage("/_Host");
app.MapRazorPages();

app.Run();