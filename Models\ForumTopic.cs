using System.ComponentModel.DataAnnotations;

namespace AgriEnergyPlatform.Models
{
    public class ForumTopic
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Title is required")]
        [StringLength(200, MinimumLength = 5, ErrorMessage = "Title must be between 5 and 200 characters")]
        public string Title { get; set; } = string.Empty;

        [Required(ErrorMessage = "Description is required")]
        [StringLength(5000, MinimumLength = 20, ErrorMessage = "Description must be between 20 and 5000 characters")]
        public string Description { get; set; } = string.Empty;  // Added Description

        public DateTime PostDate { get; set; } = DateTime.UtcNow;  // Added PostDate with default
        public string AuthorName { get; set; } = string.Empty;  // Added AuthorName
        public string? Tags { get; set; } // Comma-separated tags
        public string UserId { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow; 
        public ApplicationUser? User { get; set; }
        public bool IsPublished { get; set; }
        public virtual ICollection<Comment> Comments { get; set; } = new List<Comment>();
    }
}