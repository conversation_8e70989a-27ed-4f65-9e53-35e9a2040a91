:root {
    --primary-green: #2e7d32;
    --primary-dark: #1b5e20;
    --primary-light: #4caf50;
    --energy-orange: #f57c00;
    --energy-dark: #ef6c00;
    --light-bg: #f8f9fa;
    --border-radius: 0.5rem;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

html, body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

/* Global Styles */
.page {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

main {
    flex: 1;
}

.content {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    height: 100%;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: #fff;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
}

/* Button Styles */
.btn {
    border-radius: 0.4rem;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-green);
    border-color: var(--primary-green);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-primary {
    color: var(--primary-green);
    border-color: var(--primary-green);
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background-color: var(--primary-green);
    color: white;
}

.btn-energy {
    background-color: var(--energy-orange);
    border-color: var(--energy-orange);
    color: white;
}

.btn-energy:hover, .btn-energy:focus {
    background-color: var(--energy-dark);
    border-color: var(--energy-dark);
    color: white;
}

/* Form Controls */
.form-control {
    border-radius: 0.4rem;
    padding: 0.625rem 1rem;
    border: 1px solid #dee2e6;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-green);
    box-shadow: 0 0 0 0.25rem rgba(46, 125, 50, 0.25);
}

/* Section Styles */
.section-padding {
    padding: 5rem 0;
}

.section-title {
    margin-bottom: 3rem;
    position: relative;
    text-align: center;
}

.section-title:after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background: var(--primary-green);
    margin: 15px auto 0;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('../images/1.webp');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 120px 0;
    margin-bottom: 30px;
}

.hero-content {
    max-width: 700px;
    margin: 0 auto;
    text-align: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

/* Feature Cards */
.feature-card {
    transition: var(--transition);
    border-radius: var(--border-radius);
    overflow: hidden;
    height: 100%;
    margin-bottom: 20px;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-primary {
    border-top: 4px solid var(--primary-green);
}

.card-energy {
    border-top: 4px solid var(--energy-orange);
}

.bg-icon-green {
    background-color: rgba(46, 125, 50, 0.1);
    color: var(--primary-green);
}

.bg-icon-orange {
    background-color: rgba(255, 143, 0, 0.1);
    color: var(--energy-orange);
}

/* Testimonial Cards */
.testimonial-card {
    background-color: #fff;
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    height: 100%;
}

.testimonial-card img {
    width: 60px;
    height: 60px;
    object-fit: cover;
}

/* Insights Styles */
.insight-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.insight-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.comments-section {
    margin-top: 2rem;
}

.comment {
    padding: 1rem;
    border-radius: var(--border-radius);
    background-color: var(--light-bg);
    margin-bottom: 1rem;
}

/* Auth Pages */
.auth-container {
    background-color: var(--light-bg);
    min-height: 100vh;
    padding: 3rem 0;
}

.auth-card {
    border: none;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.auth-image {
    position: relative;
    height: 100%;
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    padding: 2rem;
}

/* Blazor Error UI */
#blazor-error-ui {
    display: none;
    position: fixed;
    bottom: 0;
    right: 0;
    padding: 1rem;
    z-index: 1000;
    background-color: #fff3cd;
    border: 1px solid #ffecb5;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.25rem;
    }
    
    .section-padding {
        padding: 3rem 0;
    }
}

/* Add these styles to your site.css file */
.card {
    border-radius: 10px;
    transition: transform 0.2s;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
}

.card:hover {
    transform: translateY(-5px);
}

.card-img-top {
    height: 200px;
    object-fit: contain;
}

.testimonials .card {
    background-color: #f8f9fa;
    border: none;
}

.section-title {
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
}

.section-title:after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background: #2e7d32;
    margin: 15px auto 0;
}

.btn {
    border-radius: 0.4rem;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #2e7d32;
    border-color: #2e7d32;
}

.btn-primary:hover, .btn-primary:focus {
    background-color: #1b5e20;
    border-color: #1b5e20;
}

.btn-outline-primary {
    color: #2e7d32;
    border-color: #2e7d32;
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background-color: #2e7d32;
    color: white;
}