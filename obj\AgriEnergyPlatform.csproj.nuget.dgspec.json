{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\AgriEnergyPlatform\\AgriEnergyPlatform.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\AgriEnergyPlatform\\AgriEnergyPlatform.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\AgriEnergyPlatform\\AgriEnergyPlatform.csproj", "projectName": "AgriEnergyPlatform", "projectPath": "C:\\Users\\<USER>\\Desktop\\AgriEnergyPlatform\\AgriEnergyPlatform.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\AgriEnergyPlatform\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Google.Apis.Gmail.v1": {"target": "Package", "version": "[1.69.0.3742, )"}, "Microsoft.AspNetCore.Authentication.Cookies": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.AspNetCore.Components": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.AspNetCore.Components.Authorization": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.4, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[8.8.0, )"}, "Microsoft.JSInterop": {"target": "Package", "version": "[9.0.4, )"}, "MimeKit": {"target": "Package", "version": "[4.12.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[8.1.1, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.8.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}