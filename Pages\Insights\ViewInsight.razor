@page "/insights/{Id:int}"
@using AgriEnergyPlatform.Services
@using AgriEnergyPlatform.Models
@using System.Security.Claims
@inject TopicService TopicService
@inject AuthenticationStateProvider AuthProvider
@inject NavigationManager NavigationManager
@inject IJSRuntime JS

<div class="insight-page">
    @if (topic == null)
    {
        <div class="loading-spinner">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Loading discussion...</p>
        </div>
    }
    else
    {
        <div class="insight-container">
            <!-- Main Insight Content -->
            <div class="insight-main">
                <div class="insight-voting">
                    <button @onclick="@(() => LikeInsight())" class="vote-btn upvote" title="Upvote this insight">
                        <i class="fas fa-caret-up"></i>
                    </button>
                    <span class="vote-count">@likesCount</span>
                </div>
                
                <div class="insight-content">
                    <div class="insight-header">
                        <h1>@topic?.Title</h1>
                        <div class="insight-meta">
                            <span class="asked">
                                Asked <time>@topic?.CreatedAt.ToString("MMMM d, yyyy 'at' h:mm tt")</time>
                            </span>
                            <span class="author">
                                <img src="/images/user-placeholder.png" class="user-avatar" width="44" height="44" alt="User avatar">
                                <a href="/users/@topic?.User?.Id">@topic?.User?.UserName</a>
                            </span>
                        </div>
                    </div>
                    
                    <!-- Description with scrollable container -->
                    <div class="description-container">
                        @((MarkupString)(topic?.Description ?? string.Empty))
                    </div>
                    
                    <div class="post-actions">
                        <div class="post-tags">
                            @foreach (var tag in topic?.Tags?.Split(',') ?? Enumerable.Empty<string>())
                            {
                                <a href="/insights/tagged/@tag" class="post-tag">@tag</a>
                            }
                        </div>
                        <div class="action-buttons">
                            <button class="action-btn" @onclick="@(() => ShareInsight())" title="Share this insight">
                                <i class="fas fa-share"></i> Share
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Comments Section -->
            <div class="comments-section">
                <h2 class="comments-header">@(topic?.Comments?.Count ?? 0) @((topic?.Comments?.Count == 1 ? "Comment" : "Comments"))</h2>
                
                @foreach (var comment in topic?.Comments?.OrderByDescending(c => c.CreatedAt) ?? Enumerable.Empty<Comment>())
                {
                    <div class="comment">
                        <div class="comment-voting">
                            <button class="vote-btn" title="Upvote this comment">
                                <i class="fas fa-caret-up"></i>
                            </button>
                        </div>
                        <div class="comment-content">
                            <div class="comment-text">
                                @((MarkupString)comment.Content)
                            </div>
                            @if (!string.IsNullOrEmpty(comment.ImageUrl))
                            {
                                <div class="comment-image">
                                    <img src="@comment.ImageUrl" alt="Comment image" />
                                </div>
                            }
                            <div class="comment-footer">
                                <span class="comment-author">
                                    <img src="/images/user-placeholder.png" class="user-avatar" width="44" height="44" alt="User avatar">
                                    <a href="/users/@comment.User?.Id">@comment.User?.Name</a>
                                </span>
                                <span class="comment-date">
                                    @comment.CreatedAt.ToString("MMMM d, yyyy 'at' h:mm tt")
                                </span>
                            </div>
                        </div>
                    </div>
                }
            </div>

            <div class="add-comment">
                <h3>Your Answer</h3>
                @if (!string.IsNullOrEmpty(commentError))
                {
                    <div class="alert alert-danger">@commentError</div>
                }
                <EditForm Model="newComment" OnValidSubmit="AddComment">
                    <div class="form-group">
                        <InputTextArea @bind-Value="newComment.Content" 
                                     class="form-control" 
                                     rows="8"
                                     placeholder="Type your comment here..."></InputTextArea>
                        <ValidationMessage For="@(() => newComment.Content)" class="text-danger small" />
                    </div>
                    
                    @if (!string.IsNullOrEmpty(newComment.ImageUrl))
                    {
                        <div class="image-preview">
                            <img src="@newComment.ImageUrl" alt="Preview" />
                            <button @onclick="@(() => RemoveImage())" class="btn-remove" title="Remove image">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    }
                    <br/>
                    <div class="form-actions ">
                        <button type="submit" class="btn-post" disabled="@(string.IsNullOrEmpty(newComment.Content))">
                            Post Comment
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    }
</div>

<style>
    /* Previous styles remain the same... */

    /* New description container styles */
    .description-container {
        max-height: 300px;
        overflow-y: auto;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 4px;
        margin: 1rem 0;
        line-height: 1.6;
        font-size: 1rem;
    }

    .description-container img {
        max-width: 100%;
        height: auto;
    }

    /* Custom scrollbar */
    .description-container::-webkit-scrollbar {
        width: 8px;
    }

    .description-container::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .description-container::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
    }

    .description-container::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Responsive adjustments */
    @@media (max-width: 768px) {
        .description-container {
            max-height: 200px;
        }
    }
</style>

@code {
    [Parameter] public int Id { get; set; }
    private ForumTopic? topic;
    private Comment newComment = new();
    private int likesCount;
    private ElementReference fileInput;

    protected override async Task OnInitializedAsync()
    {
        topic = await TopicService.GetTopicByIdAsync(Id);
        likesCount = await TopicService.GetLikesCountAsync(Id);
    }

    private string commentError = string.Empty;
    private async Task AddComment()
    {
        commentError = string.Empty;
        var authState = await AuthProvider.GetAuthenticationStateAsync();
        var userId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        if (userId == null)
        {
            commentError = "You must be logged in to comment.";
            StateHasChanged();
            return;
        }

        if (string.IsNullOrEmpty(newComment.Content))
        {
            commentError = "Comment cannot be empty.";
            StateHasChanged();
            return;
        }

        var oldCount = topic?.Comments?.Count ?? 0;
    
        await TopicService.AddCommentAsync(newComment, userId, Id);
        topic = await TopicService.GetTopicByIdAsync(Id);
        var newCount = topic?.Comments?.Count ?? 0;

        if (newCount == oldCount)
        {
            commentError = "Failed to add comment. Please make sure the topic and user exist.";
        }
        else
        {
            newComment = new Comment();
        }
        StateHasChanged();
    }

    private async Task HandleFileUpload(InputFileChangeEventArgs e)
    {
        const long maxFileSize = 1024 * 1024 * 5;
        var file = e.File;
        if (file != null && file.Size <= maxFileSize)
        {
            var buffer = new byte[file.Size];
            await file.OpenReadStream(maxFileSize).ReadAsync(buffer);
            newComment.ImageUrl = $"data:{file.ContentType};base64,{Convert.ToBase64String(buffer)}";
            StateHasChanged();
        }
    }

    private void RemoveImage()
    {
        newComment.ImageUrl = string.Empty;
        StateHasChanged();
    }

    private async Task LikeInsight()
    {
        var authState = await AuthProvider.GetAuthenticationStateAsync();
        var userId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        
        if (userId != null)
        {
            await TopicService.LikeTopicAsync(Id, userId);
            likesCount = await TopicService.GetLikesCountAsync(Id);
        }
    }

    private async Task ShareInsight()
    {
        var url = NavigationManager.Uri;
        await JS.InvokeVoidAsync("navigator.clipboard.writeText", url);
    }
}