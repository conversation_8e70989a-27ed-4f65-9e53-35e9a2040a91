@page "/energy-solutions"
@attribute [Authorize(Roles = "Admin,User,Vendor")]
@using AgriEnergyPlatform.Models
@using AgriEnergyPlatform.Services
@inject IEnergyProductService ProductService
@inject AuthenticationStateProvider AuthenticationStateProvider

<div class="container my-5">
    <div class="section-title">
        <h1>Energy Solutions Overview</h1>
        <p class="text-muted">Discover sustainable energy options for your agricultural needs</p>
    </div>

    @if (isVendor)
    {
        <div class="mb-4 d-flex justify-content-end">
            <a href="/vendor/manage-products" class="btn btn-success">
                <i class="fas fa-plus-circle me-2"></i>Add Solution
            </a>
        </div>
    }
    
    @if (products == null)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    }
    else if (!products.Any())
    {
        <div class="alert alert-info">No energy products available at the moment.</div>
    }
    else
    {
        <div class="row g-4">
            @foreach (var product in products)
            {
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-img-wrapper p-4 text-center bg-light rounded-top">
                            <img src="@product.ImageUrl" 
                                 class="card-img-top" 
                                 alt="@product.Name"
                                 style="height: 180px; object-fit: contain;"
                                 onerror="this.src='/images/energy-placeholder.svg'">
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <h5 class="card-title mb-0">@product.Name</h5>
                                <span class="badge bg-success">@product.Category</span>
                            </div>
                            <p class="card-text">@product.Description</p>
                            @if (product.Vendor != null && !string.IsNullOrEmpty(product.Vendor.CompanyName))
                            {
                                <p class="card-text"><small class="text-muted">Sold by: @product.Vendor.CompanyName</small></p>
                            }
                            <ul class="list-unstyled">
                                @foreach (var feature in product.KeyFeatures.Take(3))
                                {
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        @feature
                                    </li>
                                }
                            </ul>
                        </div>
                        <div class="card-footer bg-white border-0 rounded-bottom">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-bold text-success">@product.Price</span>
                                <a href="/energy-solutions/@product.Slug" class="btn btn-success">
                                    <i class="fas fa-arrow-right me-2"></i>View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }

@code {
    private List<EnergyProduct>? products;
    private string? errorMessage;
    private bool isVendor = false;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        isVendor = user.IsInRole("Vendor");
        Console.WriteLine("Is Vendor: " + isVendor);

        try
        {
            products = await ProductService.GetProducts();
            Console.WriteLine("Products loaded: " + products.Count);
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading products: {ex.Message}";
            products = new List<EnergyProduct>();
        }
    }
}
</div>

<style>
.card {
    border-radius: 10px;
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-5px);
}

.card-img-top {
    height: 200px;
    object-fit: contain;
}

.testimonials .card {
    background-color: #f8f9fa;
    border: none;
}
</style>