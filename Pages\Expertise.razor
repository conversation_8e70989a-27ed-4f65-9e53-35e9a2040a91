@page "/expertise"
@attribute [Authorize(Roles = "Admin,User")]
@using AgriEnergyPlatform.Services
@using AgriEnergyPlatform.Models
@using Microsoft.AspNetCore.Identity
@using System.Security.Claims
@inject IExpertiseService ExpertiseService
@inject IUserService UserService
@inject UserManager<ApplicationUser> UserManager
@inject IExpertMessageService ExpertMessageService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IJSRuntime JS

@if (currentUser != null &&
    (
        (!IsCurrentUserExpert && (currentUser.Role == "Farmer" || currentUser.Role == "Vendor"))
        || (currentUser.Role == "Expert" && !experts.Any(e => e.UserId == currentUser.Id))
    ))
{
    <div class="alert alert-info my-3">
        <strong>Want to become an Expert?</strong>
        <button class="btn btn-success ms-2" @onclick="() => ShowUpdateInfoModal(currentUser)">Opt-In as Expert</button>
    </div>
}

<div class="container my-5">
       <div class="section-title">
            <h1>Expertise Directory</h1>
            <p class="text-muted">Discover sustainable energy solutions for your agricultural needs with guidance from our experts.</p>
      </div>
    
@if (experts == null)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    }
    else if (!experts.Any())
    {
        <div class="alert alert-info">No Experts at the moment.</div>
    }
    else
    {
        <div class="row mt-4">
            @foreach (var expert in experts)
            {
               <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body">
                            <h5 class="card-title">@expert.Name</h5>
                            <h6 class="card-subtitle mb-2 text-muted text-truncate" 
                                style="max-width: 100%;" 
                                data-bs-toggle="tooltip" 
                                data-bs-placement="top" 
                                title="@expert.Specialization">
                                @((expert.Specialization != null && expert.Specialization.Length > 40) ? expert.Specialization.Substring(0, 40) + "..." : expert.Specialization)
                            </h6>
                            <p class="card-text text-truncate" 
                            style="max-width: 100%;" 
                            data-bs-toggle="tooltip" 
                            data-bs-placement="top" 
                            title="@expert.Bio">
                                @((expert.Bio != null && expert.Bio.Length > 60) ? expert.Bio.Substring(0, 60) + "..." : expert.Bio)
                            </p>
                        </div>
                        <div class="card-footer bg-transparent d-flex justify-content-between align-items-center">
                            <button class="btn btn-sm btn-outline-primary flex-grow-1 me-2" 
                                    @onclick="() => ShowContactModal(expert)"
                                    title="Send message to @expert.Name">
                                <i class="fas fa-envelope me-1"></i> Contact
                            </button>
                            @if (currentUser != null && expert.UserId == currentUser.Id)
                            {
                                <button class="btn btn-sm btn-warning flex-grow-1" 
                                        @onclick="() => ShowUpdateInfoModalForExpert(expert)"
                                        title="Edit your expert profile">
                                    <i class="fas fa-edit me-1"></i> Edit
                                </button>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    }
</div>

@if (isUpdateInfoModalOpen && userToPromote != null)
{
    <div class="modal show d-block" tabindex="-1" style="background:rgba(0,0,0,0.5)">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Update Info & Promote</h5>
                    <button type="button" class="btn-close" @onclick="CloseUpdateInfoModal"></button>
                </div>
                <div class="modal-body">
                    <label>Name</label>
                    <input class="form-control mb-2" @bind="updatedName" disabled/>
                    <label>Email</label>
                    <input class="form-control mb-2" @bind="updatedEmail" disabled/>
                    <label>Bio</label>
                    <textarea class="form-control mb-2" @bind="updatedBio"></textarea>
                    <label>Specialization</label>
                    <input class="form-control mb-2" @bind="updatedSpecialization" />
                    <label>Contact Info</label>
                    <input class="form-control mb-2" @bind="updatedContactInfo" />
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @onclick="CloseUpdateInfoModal">Cancel</button>
                    <button class="btn btn-success" @onclick="UpdateInfoWithSave">
                        @(expertToEdit != null ? "Save Changes" : "Promote to Expert")
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@if (isContactModalOpen && selectedExpert != null)
{
    <div class="modal show d-block" tabindex="-1" style="background:rgba(0,0,0,0.5)">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Contact @selectedExpert.Name</h5>
                    <button type="button" class="btn-close" @onclick="CloseContactModal"></button>
                </div>
                <div class="modal-body">
                    <textarea class="form-control mb-2" @bind="messageToExpert" placeholder="Type your message here"></textarea>
                    @if (!string.IsNullOrEmpty(sendStatus))
                    {
                        <div class="alert alert-info">@sendStatus</div>
                    }
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @onclick="CloseContactModal">Cancel</button>
                    <button class="btn btn-primary" @onclick="SendMessageToExpert">Send</button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<Expert> experts = new();
    private List<ApplicationUser> farmersAndVendors = new();
    private bool isLoading = true;
    private ApplicationUser? currentUser;
    private bool IsCurrentUserExpert => currentUser != null && experts.Any(e => e.UserId.ToString() == currentUser.Id);
    private string? updatedBio;
    private string? updatedSpecialization;
    private string? updatedContactInfo;


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JS.InvokeVoidAsync("initTooltips");
        }
    }


    protected override async Task OnInitializedAsync()
    {
        var expertUsers = await UserManager.GetUsersInRoleAsync("Expert");
        var currentExperts = await ExpertiseService.GetExpertsAsync();
        var expertUserIds = currentExperts.Select(e => e.UserId).ToHashSet();
        foreach (var user in expertUsers)
        {
            if (!expertUserIds.Contains(user.Id))
            {
                await UserService.PromoteToExpertAsync(user, "", "", user.Email);
            }
        }
        experts = await ExpertiseService.GetExpertsAsync();
        var allUsers = await UserService.GetAllUsersAsync();
        farmersAndVendors = allUsers.Where(u => u.Role == "Farmer" || u.Role == "Vendor").ToList();
        var userIdStr = await GetCurrentUserIdAsync();
        currentUser = allUsers.FirstOrDefault(u => u.Id == userIdStr);
        isLoading = false;
    }

    private async Task PromoteToExpert(ApplicationUser user)
    {
        await UserService.PromoteToExpertAsync(user, "", "", user.Email);
        // Refresh lists
        experts = await ExpertiseService.GetExpertsAsync();
        farmersAndVendors = (await UserService.GetAllUsersAsync()).Where(u => u.Role == "Farmer" || u.Role == "Vendor").ToList();
        StateHasChanged();
    }

    // Modal state
    private bool isContactModalOpen = false;
    private Expert? selectedExpert = null;
    private string messageToExpert = "";
    private string sendStatus = "";

    // Update info modal state
    private bool isUpdateInfoModalOpen = false;
    private ApplicationUser? userToPromote = null;
    private string updatedName = "";
    private string updatedEmail = "";
    private Expert? expertToEdit = null;

    private void ShowUpdateInfoModal(ApplicationUser user)
{
    Console.WriteLine($"ShowUpdateInfoModal called for user: {user?.UserName}");
    userToPromote = user;
    updatedName = user.Name ?? user.FullName ?? user.UserName;
    updatedEmail = user.Email;
    updatedBio = "";
    updatedSpecialization = "";
    updatedContactInfo = user.Email;
    expertToEdit = null;
    isUpdateInfoModalOpen = true;
    StateHasChanged();
}

    private async void ShowUpdateInfoModalForExpert(Expert expert)
    {
        var allUsers = await UserService.GetAllUsersAsync();
        userToPromote = allUsers.FirstOrDefault(u => u.Id == expert.UserId);
        if (userToPromote != null)
        {
            updatedName = userToPromote.Name ?? userToPromote.FullName ?? userToPromote.UserName;
            updatedEmail = userToPromote.Email;
            updatedBio = expert.Bio;
            updatedSpecialization = expert.Specialization;
            updatedContactInfo = expert.ContactInfo;
            expertToEdit = expert;
            isUpdateInfoModalOpen = true;
        }
    }

    private void CloseUpdateInfoModal()
    {
        isUpdateInfoModalOpen = false;
        userToPromote = null;
        updatedName = "";
        updatedEmail = "";
        updatedBio = "";
        updatedSpecialization = "";
        updatedContactInfo = "";
        updatedEmail = "";
        updatedBio = "";
        updatedSpecialization = "";
        updatedContactInfo = "";
        expertToEdit = null;
    }

    private async Task UpdateInfoWithSave()
{
    try
    {
        if (userToPromote == null)
        {
            errorMsg = "User to promote is null.";
            return;
        }
        userToPromote.Name = updatedName;
        userToPromote.Email = updatedEmail;
        await UserService.PromoteToExpertAsync(userToPromote, updatedBio, updatedSpecialization, updatedContactInfo);
        experts = await ExpertiseService.GetExpertsAsync();
        farmersAndVendors = (await UserService.GetAllUsersAsync()).Where(u => u.Role == "Farmer" || u.Role == "Vendor").ToList();
        isUpdateInfoModalOpen = false;
        StateHasChanged();
    }
    catch (Exception ex)
    {
        errorMsg = $"Error promoting to expert: {ex.Message}";
        Console.WriteLine(errorMsg);
    }
}

    private string errorMsg = string.Empty;

    private void ShowContactModal(Expert expert)
{
    Console.WriteLine($"ShowContactModal called for expert: {expert?.Name}");
    selectedExpert = expert;
    messageToExpert = "";
    sendStatus = "";
    isContactModalOpen = true;
    StateHasChanged();
}
    private void CloseContactModal()
    {
        isContactModalOpen = false;
        selectedExpert = null;
        messageToExpert = "";
        sendStatus = "";
    }

    private async Task<string?> GetCurrentUserIdAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        return user?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }
    private async Task<ApplicationUser?> GetCurrentUserAsync()
{
    var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
    var user = authState.User;
    var userId = user?.FindFirst(ClaimTypes.NameIdentifier)?.Value;

    if (string.IsNullOrEmpty(userId))
        return null;

    var allUsers = await UserService.GetAllUsersAsync();
    return allUsers.FirstOrDefault(u => u.Id == userId);
}

    private async Task SendMessageToExpert()
{
    Console.WriteLine("Selected expert: " + selectedExpert?.Name);
    sendStatus = string.Empty;
    if (selectedExpert != null && !string.IsNullOrWhiteSpace(messageToExpert))
    {
        var userIdStr = await GetCurrentUserIdAsync();
        if (string.IsNullOrEmpty(userIdStr))
        {
            sendStatus = "Could not determine your user ID.";
            return;
        }
        try
        {
            await ExpertMessageService.SendMessageAsync(selectedExpert.Id, userIdStr, messageToExpert);
            sendStatus = "Message sent!";
            messageToExpert = string.Empty;
        }
        catch (Exception ex)
        {
            sendStatus = $"Error sending message: {ex.Message}";
        }
    }
    else
    {
        sendStatus = "Please enter a message.";
    }
}
}

<script>
    function initTooltips() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
</script>