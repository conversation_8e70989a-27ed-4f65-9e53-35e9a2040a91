using Microsoft.EntityFrameworkCore;
using AgriEnergyPlatform.Data;
using AgriEnergyPlatform.Models;

namespace AgriEnergyPlatform.Services
{
    public class EnergyProductService : IEnergyProductService
    {
        private readonly ApplicationDbContext _context;

        public EnergyProductService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<EnergyProduct>> GetProducts(string? category = null)
        {
            var query = _context.EnergyProducts.Include(p => p.Vendor).AsQueryable();
            
            if (!string.IsNullOrEmpty(category))
                query = query.Where(p => p.Category == category);

            return await query.ToListAsync();
        }

        public async Task<EnergyProduct?> GetProductById(int id)
        {
            return await _context.EnergyProducts.Include(p => p.Vendor).FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<EnergyProduct> CreateProductAsync(EnergyProduct product)
        {
            if (product == null) throw new ArgumentNullException(nameof(product));

            product.CreatedAt = DateTime.UtcNow;
            _context.EnergyProducts.Add(product);
            await _context.SaveChangesAsync();
            return product;
        }

        public async Task<EnergyProduct?> GetProductBySlugAsync(string slug)
        {
            return await _context.EnergyProducts
                                 .Include(p => p.Vendor) // Ensure Vendor details are loaded
                                 .FirstOrDefaultAsync(p => p.Slug == slug);
        }
    }
}