@page "/insights/view" 
@using AgriEnergyPlatform.Services
@using AgriEnergyPlatform.Models
@using System.Security.Claims
@inject TopicService TopicService
@inject AuthenticationStateProvider AuthProvider

<h3>Insights</h3>



@foreach (var topic in publishedTopics)
{
    <div class="topic">
        <h4>@topic?.Title</h4>
        <p>By: @topic?.User?.UserName</p>
        <p>@topic?.Description</p>
        
        <h5>Comments</h5>
        @foreach (var comment in topic?.Comments ?? Enumerable.Empty<Comment>())
        {
            <div class="comment">
                <p>@comment?.User?.UserName: @comment?.Content</p>
            </div>
        }
        
        <EditForm Model="newComments[topic.Id]" OnValidSubmit="() => AddComment(topic.Id)">
            <InputTextArea @bind-Value="newComments[topic.Id].Content" placeholder="Add comment..." />
            <button type="submit">Post Comment</button>
        </EditForm>
    </div>
}

@code {
    private List<ForumTopic> publishedTopics = new();
    private Dictionary<int, Comment> newComments = new();
    private ForumTopic? content;
    
    protected override async Task OnInitializedAsync()
    {
        publishedTopics = await TopicService.GetPublishedTopicsAsync();
        foreach (var topic in publishedTopics)
        {
            newComments[topic.Id] = new Comment();
        }
    }
    
    private async Task AddComment(int topicId)
    {
        var authState = await AuthProvider.GetAuthenticationStateAsync();
        var userId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        
        if (userId != null)
        {
            var comment = newComments[topicId];
            comment.ForumTopicId = topicId;
            await TopicService.AddCommentAsync(comment, userId, topicId);
            publishedTopics = await TopicService.GetPublishedTopicsAsync();
            newComments[topicId] = new Comment();
        }
    }
}