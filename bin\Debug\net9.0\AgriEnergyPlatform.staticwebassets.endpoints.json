{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "bootstrap/css/bootstrap-grid.css", "AssetFile": "bootstrap/css/bootstrap-grid.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148060409"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6753"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"nxk5KakagvHxNEYUcPV9gkxz0mK6SirVvRBt8gQ6R7U=\""}, {"Name": "ETag", "Value": "W/\"3UlrIiX4eEhioXBUvLtvb6zRm8NmA4wf5JW3CVP6g9M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3UlrIiX4eEhioXBUvLtvb6zRm8NmA4wf5JW3CVP6g9M="}]}, {"Route": "bootstrap/css/bootstrap-grid.css", "AssetFile": "bootstrap/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "74407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3UlrIiX4eEhioXBUvLtvb6zRm8NmA4wf5JW3CVP6g9M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3UlrIiX4eEhioXBUvLtvb6zRm8NmA4wf5JW3CVP6g9M="}]}, {"Route": "bootstrap/css/bootstrap-grid.css.gz", "AssetFile": "bootstrap/css/bootstrap-grid.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6753"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"nxk5KakagvHxNEYUcPV9gkxz0mK6SirVvRBt8gQ6R7U=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nxk5KakagvHxNEYUcPV9gkxz0mK6SirVvRBt8gQ6R7U="}]}, {"Route": "bootstrap/css/bootstrap-grid.css.map", "AssetFile": "bootstrap/css/bootstrap-grid.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030532487"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32751"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MelTAs9e7C34rIaVtF8SdDNCEQJrV0XvGvJPhNmJcSE=\""}, {"Name": "ETag", "Value": "W/\"NhcUlLf+d4FwafiDTOYddnZQVGgyXIewp0da3zIstiM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NhcUlLf+d4FwafiDTOYddnZQVGgyXIewp0da3zIstiM="}]}, {"Route": "bootstrap/css/bootstrap-grid.css.map", "AssetFile": "bootstrap/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "203340"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NhcUlLf+d4FwafiDTOYddnZQVGgyXIewp0da3zIstiM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NhcUlLf+d4FwafiDTOYddnZQVGgyXIewp0da3zIstiM="}]}, {"Route": "bootstrap/css/bootstrap-grid.css.map.gz", "AssetFile": "bootstrap/css/bootstrap-grid.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32751"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MelTAs9e7C34rIaVtF8SdDNCEQJrV0XvGvJPhNmJcSE=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MelTAs9e7C34rIaVtF8SdDNCEQJrV0XvGvJPhNmJcSE="}]}, {"Route": "bootstrap/css/bootstrap-grid.css.ylu1e6mjaa.map", "AssetFile": "bootstrap/css/bootstrap-grid.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030532487"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32751"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MelTAs9e7C34rIaVtF8SdDNCEQJrV0XvGvJPhNmJcSE=\""}, {"Name": "ETag", "Value": "W/\"NhcUlLf+d4FwafiDTOYddnZQVGgyXIewp0da3zIstiM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ylu1e6mjaa"}, {"Name": "integrity", "Value": "sha256-NhcUlLf+d4FwafiDTOYddnZQVGgyXIewp0da3zIstiM="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.css.map"}]}, {"Route": "bootstrap/css/bootstrap-grid.css.ylu1e6mjaa.map", "AssetFile": "bootstrap/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "203340"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NhcUlLf+d4FwafiDTOYddnZQVGgyXIewp0da3zIstiM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ylu1e6mjaa"}, {"Name": "integrity", "Value": "sha256-NhcUlLf+d4FwafiDTOYddnZQVGgyXIewp0da3zIstiM="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.css.map"}]}, {"Route": "bootstrap/css/bootstrap-grid.css.ylu1e6mjaa.map.gz", "AssetFile": "bootstrap/css/bootstrap-grid.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32751"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MelTAs9e7C34rIaVtF8SdDNCEQJrV0XvGvJPhNmJcSE=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ylu1e6mjaa"}, {"Name": "integrity", "Value": "sha256-MelTAs9e7C34rIaVtF8SdDNCEQJrV0XvGvJPhNmJcSE="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap-grid.hrgcag23ni.css", "AssetFile": "bootstrap/css/bootstrap-grid.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148060409"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6753"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"nxk5KakagvHxNEYUcPV9gkxz0mK6SirVvRBt8gQ6R7U=\""}, {"Name": "ETag", "Value": "W/\"3UlrIiX4eEhioXBUvLtvb6zRm8NmA4wf5JW3CVP6g9M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrgcag23ni"}, {"Name": "integrity", "Value": "sha256-3UlrIiX4eEhioXBUvLtvb6zRm8NmA4wf5JW3CVP6g9M="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.css"}]}, {"Route": "bootstrap/css/bootstrap-grid.hrgcag23ni.css", "AssetFile": "bootstrap/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "74407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3UlrIiX4eEhioXBUvLtvb6zRm8NmA4wf5JW3CVP6g9M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrgcag23ni"}, {"Name": "integrity", "Value": "sha256-3UlrIiX4eEhioXBUvLtvb6zRm8NmA4wf5JW3CVP6g9M="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.css"}]}, {"Route": "bootstrap/css/bootstrap-grid.hrgcag23ni.css.gz", "AssetFile": "bootstrap/css/bootstrap-grid.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6753"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"nxk5KakagvHxNEYUcPV9gkxz0mK6SirVvRBt8gQ6R7U=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrgcag23ni"}, {"Name": "integrity", "Value": "sha256-nxk5KakagvHxNEYUcPV9gkxz0mK6SirVvRBt8gQ6R7U="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.css.gz"}]}, {"Route": "bootstrap/css/bootstrap-grid.min.css", "AssetFile": "bootstrap/css/bootstrap-grid.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167392032"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Cuo2IHwTVEEbJwYz94xowbKYVzehT00leJbBefdduA8=\""}, {"Name": "ETag", "Value": "W/\"Sv3SrvBoCJpJUKuYGfKGC0cOHvaQpgvUs5M1w0ZZh64=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Sv3SrvBoCJpJUKuYGfKGC0cOHvaQpgvUs5M1w0ZZh64="}]}, {"Route": "bootstrap/css/bootstrap-grid.min.css", "AssetFile": "bootstrap/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51794"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Sv3SrvBoCJpJUKuYGfKGC0cOHvaQpgvUs5M1w0ZZh64=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Sv3SrvBoCJpJUKuYGfKGC0cOHvaQpgvUs5M1w0ZZh64="}]}, {"Route": "bootstrap/css/bootstrap-grid.min.css.0e0dis3st6.map", "AssetFile": "bootstrap/css/bootstrap-grid.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072632191"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13767"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"eDca4pD+Ba3X2zuNqKrnEArFTm/v4V46eRWa7EDqUEM=\""}, {"Name": "ETag", "Value": "W/\"OLVBUrGRZnw3giYqXL0t4Tvl/eqib1mdgCxtUMNUyaQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0e0dis3st6"}, {"Name": "integrity", "Value": "sha256-OLVBUrGRZnw3giYqXL0t4Tvl/eqib1mdgCxtUMNUyaQ="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap-grid.min.css.0e0dis3st6.map", "AssetFile": "bootstrap/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "115912"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OLVBUrGRZnw3giYqXL0t4Tvl/eqib1mdgCxtUMNUyaQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0e0dis3st6"}, {"Name": "integrity", "Value": "sha256-OLVBUrGRZnw3giYqXL0t4Tvl/eqib1mdgCxtUMNUyaQ="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap-grid.min.css.0e0dis3st6.map.gz", "AssetFile": "bootstrap/css/bootstrap-grid.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13767"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"eDca4pD+Ba3X2zuNqKrnEArFTm/v4V46eRWa7EDqUEM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0e0dis3st6"}, {"Name": "integrity", "Value": "sha256-eDca4pD+Ba3X2zuNqKrnEArFTm/v4V46eRWa7EDqUEM="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.min.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap-grid.min.css.gz", "AssetFile": "bootstrap/css/bootstrap-grid.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Cuo2IHwTVEEbJwYz94xowbKYVzehT00leJbBefdduA8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cuo2IHwTVEEbJwYz94xowbKYVzehT00leJbBefdduA8="}]}, {"Route": "bootstrap/css/bootstrap-grid.min.css.map", "AssetFile": "bootstrap/css/bootstrap-grid.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072632191"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13767"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"eDca4pD+Ba3X2zuNqKrnEArFTm/v4V46eRWa7EDqUEM=\""}, {"Name": "ETag", "Value": "W/\"OLVBUrGRZnw3giYqXL0t4Tvl/eqib1mdgCxtUMNUyaQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OLVBUrGRZnw3giYqXL0t4Tvl/eqib1mdgCxtUMNUyaQ="}]}, {"Route": "bootstrap/css/bootstrap-grid.min.css.map", "AssetFile": "bootstrap/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "115912"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OLVBUrGRZnw3giYqXL0t4Tvl/eqib1mdgCxtUMNUyaQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OLVBUrGRZnw3giYqXL0t4Tvl/eqib1mdgCxtUMNUyaQ="}]}, {"Route": "bootstrap/css/bootstrap-grid.min.css.map.gz", "AssetFile": "bootstrap/css/bootstrap-grid.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13767"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"eDca4pD+Ba3X2zuNqKrnEArFTm/v4V46eRWa7EDqUEM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eDca4pD+Ba3X2zuNqKrnEArFTm/v4V46eRWa7EDqUEM="}]}, {"Route": "bootstrap/css/bootstrap-grid.min.iuspjre6m7.css", "AssetFile": "bootstrap/css/bootstrap-grid.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167392032"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Cuo2IHwTVEEbJwYz94xowbKYVzehT00leJbBefdduA8=\""}, {"Name": "ETag", "Value": "W/\"Sv3SrvBoCJpJUKuYGfKGC0cOHvaQpgvUs5M1w0ZZh64=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iuspjre6m7"}, {"Name": "integrity", "Value": "sha256-Sv3SrvBoCJpJUKuYGfKGC0cOHvaQpgvUs5M1w0ZZh64="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.min.css"}]}, {"Route": "bootstrap/css/bootstrap-grid.min.iuspjre6m7.css", "AssetFile": "bootstrap/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51794"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Sv3SrvBoCJpJUKuYGfKGC0cOHvaQpgvUs5M1w0ZZh64=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iuspjre6m7"}, {"Name": "integrity", "Value": "sha256-Sv3SrvBoCJpJUKuYGfKGC0cOHvaQpgvUs5M1w0ZZh64="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.min.css"}]}, {"Route": "bootstrap/css/bootstrap-grid.min.iuspjre6m7.css.gz", "AssetFile": "bootstrap/css/bootstrap-grid.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Cuo2IHwTVEEbJwYz94xowbKYVzehT00leJbBefdduA8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iuspjre6m7"}, {"Name": "integrity", "Value": "sha256-Cuo2IHwTVEEbJwYz94xowbKYVzehT00leJbBefdduA8="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.min.css.gz"}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.css", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148038490"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6754"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YwBpOqHeUUj/fNfgacySL3IA/7QKxb0ftokfVDeqyFY=\""}, {"Name": "ETag", "Value": "W/\"Xd12ivIbsfGYqnkhfirlI0/TEew2qvpxdeXYOlyLZD8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xd12ivIbsfGYqnkhfirlI0/TEew2qvpxdeXYOlyLZD8="}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.css", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "74480"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Xd12ivIbsfGYqnkhfirlI0/TEew2qvpxdeXYOlyLZD8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xd12ivIbsfGYqnkhfirlI0/TEew2qvpxdeXYOlyLZD8="}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.css.ex5ge62g4d.map", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030533419"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32750"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4CXAbgmQ/4PXWiyBADTT10IzDNuvoeNUB0cHg4Y1jTE=\""}, {"Name": "ETag", "Value": "W/\"/rejrZhJKwb7j/GPi5M1HfUpo4CUgIXxGXhGcaqco0M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ex5ge62g4d"}, {"Name": "integrity", "Value": "sha256-/rejrZhJKwb7j/GPi5M1HfUpo4CUgIXxGXhGcaqco0M="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.rtl.css.map"}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.css.ex5ge62g4d.map", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "203344"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/rejrZhJKwb7j/GPi5M1HfUpo4CUgIXxGXhGcaqco0M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ex5ge62g4d"}, {"Name": "integrity", "Value": "sha256-/rejrZhJKwb7j/GPi5M1HfUpo4CUgIXxGXhGcaqco0M="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.rtl.css.map"}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.css.ex5ge62g4d.map.gz", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32750"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4CXAbgmQ/4PXWiyBADTT10IzDNuvoeNUB0cHg4Y1jTE=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ex5ge62g4d"}, {"Name": "integrity", "Value": "sha256-4CXAbgmQ/4PXWiyBADTT10IzDNuvoeNUB0cHg4Y1jTE="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.rtl.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.css.gz", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6754"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YwBpOqHeUUj/fNfgacySL3IA/7QKxb0ftokfVDeqyFY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YwBpOqHeUUj/fNfgacySL3IA/7QKxb0ftokfVDeqyFY="}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.css.map", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030533419"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32750"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4CXAbgmQ/4PXWiyBADTT10IzDNuvoeNUB0cHg4Y1jTE=\""}, {"Name": "ETag", "Value": "W/\"/rejrZhJKwb7j/GPi5M1HfUpo4CUgIXxGXhGcaqco0M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/rejrZhJKwb7j/GPi5M1HfUpo4CUgIXxGXhGcaqco0M="}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.css.map", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "203344"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/rejrZhJKwb7j/GPi5M1HfUpo4CUgIXxGXhGcaqco0M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/rejrZhJKwb7j/GPi5M1HfUpo4CUgIXxGXhGcaqco0M="}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.css.map.gz", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32750"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4CXAbgmQ/4PXWiyBADTT10IzDNuvoeNUB0cHg4Y1jTE=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4CXAbgmQ/4PXWiyBADTT10IzDNuvoeNUB0cHg4Y1jTE="}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.min.btsmoax2ee.css", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167364017"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5974"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IfG5mD5LTTpLiK1/sp4gVec3vqopTIa/aGqzsXdhC8k=\""}, {"Name": "ETag", "Value": "W/\"X0sFX4Ty1hY0xwlgFdz5bKE0K822yCs33V+WKX0SJtg=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "btsmoax2ee"}, {"Name": "integrity", "Value": "sha256-X0sFX4Ty1hY0xwlgFdz5bKE0K822yCs33V+WKX0SJtg="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.rtl.min.css"}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.min.btsmoax2ee.css", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51869"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"X0sFX4Ty1hY0xwlgFdz5bKE0K822yCs33V+WKX0SJtg=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "btsmoax2ee"}, {"Name": "integrity", "Value": "sha256-X0sFX4Ty1hY0xwlgFdz5bKE0K822yCs33V+WKX0SJtg="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.rtl.min.css"}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.min.btsmoax2ee.css.gz", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5974"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IfG5mD5LTTpLiK1/sp4gVec3vqopTIa/aGqzsXdhC8k=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "btsmoax2ee"}, {"Name": "integrity", "Value": "sha256-IfG5mD5LTTpLiK1/sp4gVec3vqopTIa/aGqzsXdhC8k="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.rtl.min.css.gz"}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.min.css", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167364017"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5974"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IfG5mD5LTTpLiK1/sp4gVec3vqopTIa/aGqzsXdhC8k=\""}, {"Name": "ETag", "Value": "W/\"X0sFX4Ty1hY0xwlgFdz5bKE0K822yCs33V+WKX0SJtg=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X0sFX4Ty1hY0xwlgFdz5bKE0K822yCs33V+WKX0SJtg="}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.min.css", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51869"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"X0sFX4Ty1hY0xwlgFdz5bKE0K822yCs33V+WKX0SJtg=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X0sFX4Ty1hY0xwlgFdz5bKE0K822yCs33V+WKX0SJtg="}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.min.css.ausdus18s2.map", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072584743"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13776"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"djxeqUt7Unskv2HR6xDDY1Tyclp92m+c8plt3/7B+C0=\""}, {"Name": "ETag", "Value": "W/\"vk6GRv2uAxWjBuEQBLEDnXD6z5lhX+VTjZyFped8IK8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausdus18s2"}, {"Name": "integrity", "Value": "sha256-vk6GRv2uAxWjBuEQBLEDnXD6z5lhX+VTjZyFped8IK8="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.rtl.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.min.css.ausdus18s2.map", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "115989"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"vk6GRv2uAxWjBuEQBLEDnXD6z5lhX+VTjZyFped8IK8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausdus18s2"}, {"Name": "integrity", "Value": "sha256-vk6GRv2uAxWjBuEQBLEDnXD6z5lhX+VTjZyFped8IK8="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.rtl.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.min.css.ausdus18s2.map.gz", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13776"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"djxeqUt7Unskv2HR6xDDY1Tyclp92m+c8plt3/7B+C0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausdus18s2"}, {"Name": "integrity", "Value": "sha256-djxeqUt7Unskv2HR6xDDY1Tyclp92m+c8plt3/7B+C0="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.rtl.min.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.min.css.gz", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5974"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IfG5mD5LTTpLiK1/sp4gVec3vqopTIa/aGqzsXdhC8k=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IfG5mD5LTTpLiK1/sp4gVec3vqopTIa/aGqzsXdhC8k="}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072584743"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13776"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"djxeqUt7Unskv2HR6xDDY1Tyclp92m+c8plt3/7B+C0=\""}, {"Name": "ETag", "Value": "W/\"vk6GRv2uAxWjBuEQBLEDnXD6z5lhX+VTjZyFped8IK8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vk6GRv2uAxWjBuEQBLEDnXD6z5lhX+VTjZyFped8IK8="}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "115989"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"vk6GRv2uAxWjBuEQBLEDnXD6z5lhX+VTjZyFped8IK8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vk6GRv2uAxWjBuEQBLEDnXD6z5lhX+VTjZyFped8IK8="}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.min.css.map.gz", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13776"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"djxeqUt7Unskv2HR6xDDY1Tyclp92m+c8plt3/7B+C0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-djxeqUt7Unskv2HR6xDDY1Tyclp92m+c8plt3/7B+C0="}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.zchflqobp4.css", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148038490"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6754"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YwBpOqHeUUj/fNfgacySL3IA/7QKxb0ftokfVDeqyFY=\""}, {"Name": "ETag", "Value": "W/\"Xd12ivIbsfGYqnkhfirlI0/TEew2qvpxdeXYOlyLZD8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zchflqobp4"}, {"Name": "integrity", "Value": "sha256-Xd12ivIbsfGYqnkhfirlI0/TEew2qvpxdeXYOlyLZD8="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.rtl.css"}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.zchflqobp4.css", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "74480"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Xd12ivIbsfGYqnkhfirlI0/TEew2qvpxdeXYOlyLZD8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zchflqobp4"}, {"Name": "integrity", "Value": "sha256-Xd12ivIbsfGYqnkhfirlI0/TEew2qvpxdeXYOlyLZD8="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.rtl.css"}]}, {"Route": "bootstrap/css/bootstrap-grid.rtl.zchflqobp4.css.gz", "AssetFile": "bootstrap/css/bootstrap-grid.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6754"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YwBpOqHeUUj/fNfgacySL3IA/7QKxb0ftokfVDeqyFY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zchflqobp4"}, {"Name": "integrity", "Value": "sha256-YwBpOqHeUUj/fNfgacySL3IA/7QKxb0ftokfVDeqyFY="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-grid.rtl.css.gz"}]}, {"Route": "bootstrap/css/bootstrap-reboot.css", "AssetFile": "bootstrap/css/bootstrap-reboot.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000293599530"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3405"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OnsfTV8j53tx87AFYqIYYaH338z3f5Optl4HbtHW6tI=\""}, {"Name": "ETag", "Value": "W/\"VNeHJwjUUcBJgZtliAn4mf5qLjnMoDb+uO4HAjaUILI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VNeHJwjUUcBJgZtliAn4mf5qLjnMoDb+uO4HAjaUILI="}]}, {"Route": "bootstrap/css/bootstrap-reboot.css", "AssetFile": "bootstrap/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12661"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VNeHJwjUUcBJgZtliAn4mf5qLjnMoDb+uO4HAjaUILI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VNeHJwjUUcBJgZtliAn4mf5qLjnMoDb+uO4HAjaUILI="}]}, {"Route": "bootstrap/css/bootstrap-reboot.css.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3405"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OnsfTV8j53tx87AFYqIYYaH338z3f5Optl4HbtHW6tI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OnsfTV8j53tx87AFYqIYYaH338z3f5Optl4HbtHW6tI="}]}, {"Route": "bootstrap/css/bootstrap-reboot.css.jszzto7yfq.map", "AssetFile": "bootstrap/css/bootstrap-reboot.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038737168"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25814"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"W87bpqe53XWuiTrVQD5mYDCO0nu1QVXLpPJ9UxCJg2E=\""}, {"Name": "ETag", "Value": "W/\"8yzFR7YH9QpPE8dqXFNktD2KtgAXA+2R9ojj74tVoTI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jszzto7yfq"}, {"Name": "integrity", "Value": "sha256-8yzFR7YH9QpPE8dqXFNktD2KtgAXA+2R9ojj74tVoTI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.css.map"}]}, {"Route": "bootstrap/css/bootstrap-reboot.css.jszzto7yfq.map", "AssetFile": "bootstrap/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "129565"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8yzFR7YH9QpPE8dqXFNktD2KtgAXA+2R9ojj74tVoTI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jszzto7yfq"}, {"Name": "integrity", "Value": "sha256-8yzFR7YH9QpPE8dqXFNktD2KtgAXA+2R9ojj74tVoTI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.css.map"}]}, {"Route": "bootstrap/css/bootstrap-reboot.css.jszzto7yfq.map.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25814"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"W87bpqe53XWuiTrVQD5mYDCO0nu1QVXLpPJ9UxCJg2E=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jszzto7yfq"}, {"Name": "integrity", "Value": "sha256-W87bpqe53XWuiTrVQD5mYDCO0nu1QVXLpPJ9UxCJg2E="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap-reboot.css.map", "AssetFile": "bootstrap/css/bootstrap-reboot.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038737168"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25814"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"W87bpqe53XWuiTrVQD5mYDCO0nu1QVXLpPJ9UxCJg2E=\""}, {"Name": "ETag", "Value": "W/\"8yzFR7YH9QpPE8dqXFNktD2KtgAXA+2R9ojj74tVoTI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8yzFR7YH9QpPE8dqXFNktD2KtgAXA+2R9ojj74tVoTI="}]}, {"Route": "bootstrap/css/bootstrap-reboot.css.map", "AssetFile": "bootstrap/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "129565"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8yzFR7YH9QpPE8dqXFNktD2KtgAXA+2R9ojj74tVoTI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8yzFR7YH9QpPE8dqXFNktD2KtgAXA+2R9ojj74tVoTI="}]}, {"Route": "bootstrap/css/bootstrap-reboot.css.map.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25814"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"W87bpqe53XWuiTrVQD5mYDCO0nu1QVXLpPJ9UxCJg2E=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-W87bpqe53XWuiTrVQD5mYDCO0nu1QVXLpPJ9UxCJg2E="}]}, {"Route": "bootstrap/css/bootstrap-reboot.g5tvmjraes.css", "AssetFile": "bootstrap/css/bootstrap-reboot.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000293599530"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3405"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OnsfTV8j53tx87AFYqIYYaH338z3f5Optl4HbtHW6tI=\""}, {"Name": "ETag", "Value": "W/\"VNeHJwjUUcBJgZtliAn4mf5qLjnMoDb+uO4HAjaUILI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g5tvmjraes"}, {"Name": "integrity", "Value": "sha256-VNeHJwjUUcBJgZtliAn4mf5qLjnMoDb+uO4HAjaUILI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.css"}]}, {"Route": "bootstrap/css/bootstrap-reboot.g5tvmjraes.css", "AssetFile": "bootstrap/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12661"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VNeHJwjUUcBJgZtliAn4mf5qLjnMoDb+uO4HAjaUILI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g5tvmjraes"}, {"Name": "integrity", "Value": "sha256-VNeHJwjUUcBJgZtliAn4mf5qLjnMoDb+uO4HAjaUILI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.css"}]}, {"Route": "bootstrap/css/bootstrap-reboot.g5tvmjraes.css.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3405"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OnsfTV8j53tx87AFYqIYYaH338z3f5Optl4HbtHW6tI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g5tvmjraes"}, {"Name": "integrity", "Value": "sha256-OnsfTV8j53tx87AFYqIYYaH338z3f5Optl4HbtHW6tI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.css.gz"}]}, {"Route": "bootstrap/css/bootstrap-reboot.min.css", "AssetFile": "bootstrap/css/bootstrap-reboot.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000310848617"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3216"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dp9FpLgVgqd99yzPdK4FEQIhud52iujRVlXpBM5paQM=\""}, {"Name": "ETag", "Value": "W/\"SJ4XE93KDoXcO9/cIeBex7uF3bPvJ8Ja/N9a+VjPA4M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SJ4XE93KDoXcO9/cIeBex7uF3bPvJ8Ja/N9a+VjPA4M="}]}, {"Route": "bootstrap/css/bootstrap-reboot.min.css", "AssetFile": "bootstrap/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SJ4XE93KDoXcO9/cIeBex7uF3bPvJ8Ja/N9a+VjPA4M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SJ4XE93KDoXcO9/cIeBex7uF3bPvJ8Ja/N9a+VjPA4M="}]}, {"Route": "bootstrap/css/bootstrap-reboot.min.css.anmneyptv7.map", "AssetFile": "bootstrap/css/bootstrap-reboot.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079535513"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12572"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"pVOjx8zm3shZFDqBX0cjVLpzyTF1lcLu4mnItXU5i00=\""}, {"Name": "ETag", "Value": "W/\"mse9XCDMuMmSRcBlHRdog3+zNSUnhTt3gsebnEcQ3aI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "anmneyptv7"}, {"Name": "integrity", "Value": "sha256-mse9XCDMuMmSRcBlHRdog3+zNSUnhTt3gsebnEcQ3aI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap-reboot.min.css.anmneyptv7.map", "AssetFile": "bootstrap/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51368"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"mse9XCDMuMmSRcBlHRdog3+zNSUnhTt3gsebnEcQ3aI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "anmneyptv7"}, {"Name": "integrity", "Value": "sha256-mse9XCDMuMmSRcBlHRdog3+zNSUnhTt3gsebnEcQ3aI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap-reboot.min.css.anmneyptv7.map.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12572"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"pVOjx8zm3shZFDqBX0cjVLpzyTF1lcLu4mnItXU5i00=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "anmneyptv7"}, {"Name": "integrity", "Value": "sha256-pVOjx8zm3shZFDqBX0cjVLpzyTF1lcLu4mnItXU5i00="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.min.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap-reboot.min.css.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3216"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dp9FpLgVgqd99yzPdK4FEQIhud52iujRVlXpBM5paQM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dp9FpLgVgqd99yzPdK4FEQIhud52iujRVlXpBM5paQM="}]}, {"Route": "bootstrap/css/bootstrap-reboot.min.css.map", "AssetFile": "bootstrap/css/bootstrap-reboot.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079535513"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12572"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"pVOjx8zm3shZFDqBX0cjVLpzyTF1lcLu4mnItXU5i00=\""}, {"Name": "ETag", "Value": "W/\"mse9XCDMuMmSRcBlHRdog3+zNSUnhTt3gsebnEcQ3aI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mse9XCDMuMmSRcBlHRdog3+zNSUnhTt3gsebnEcQ3aI="}]}, {"Route": "bootstrap/css/bootstrap-reboot.min.css.map", "AssetFile": "bootstrap/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51368"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"mse9XCDMuMmSRcBlHRdog3+zNSUnhTt3gsebnEcQ3aI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mse9XCDMuMmSRcBlHRdog3+zNSUnhTt3gsebnEcQ3aI="}]}, {"Route": "bootstrap/css/bootstrap-reboot.min.css.map.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12572"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"pVOjx8zm3shZFDqBX0cjVLpzyTF1lcLu4mnItXU5i00=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pVOjx8zm3shZFDqBX0cjVLpzyTF1lcLu4mnItXU5i00="}]}, {"Route": "bootstrap/css/bootstrap-reboot.min.gendevk7hn.css", "AssetFile": "bootstrap/css/bootstrap-reboot.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000310848617"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3216"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dp9FpLgVgqd99yzPdK4FEQIhud52iujRVlXpBM5paQM=\""}, {"Name": "ETag", "Value": "W/\"SJ4XE93KDoXcO9/cIeBex7uF3bPvJ8Ja/N9a+VjPA4M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gendevk7hn"}, {"Name": "integrity", "Value": "sha256-SJ4XE93KDoXcO9/cIeBex7uF3bPvJ8Ja/N9a+VjPA4M="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.min.css"}]}, {"Route": "bootstrap/css/bootstrap-reboot.min.gendevk7hn.css", "AssetFile": "bootstrap/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SJ4XE93KDoXcO9/cIeBex7uF3bPvJ8Ja/N9a+VjPA4M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gendevk7hn"}, {"Name": "integrity", "Value": "sha256-SJ4XE93KDoXcO9/cIeBex7uF3bPvJ8Ja/N9a+VjPA4M="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.min.css"}]}, {"Route": "bootstrap/css/bootstrap-reboot.min.gendevk7hn.css.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3216"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dp9FpLgVgqd99yzPdK4FEQIhud52iujRVlXpBM5paQM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gendevk7hn"}, {"Name": "integrity", "Value": "sha256-dp9FpLgVgqd99yzPdK4FEQIhud52iujRVlXpBM5paQM="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.min.css.gz"}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.cg64932msi.css", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000294550810"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"v3pwxTmdp3AUJ0GA86s+grW/VNd2eY0WdotmZhVLaEA=\""}, {"Name": "ETag", "Value": "W/\"VNG6ooQa6SL8Cut+eozIV8WqAMsRS9mZuLkgaZGPyUs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cg64932msi"}, {"Name": "integrity", "Value": "sha256-VNG6ooQa6SL8Cut+eozIV8WqAMsRS9mZuLkgaZGPyUs="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.rtl.css"}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.cg64932msi.css", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12651"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VNG6ooQa6SL8Cut+eozIV8WqAMsRS9mZuLkgaZGPyUs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cg64932msi"}, {"Name": "integrity", "Value": "sha256-VNG6ooQa6SL8Cut+eozIV8WqAMsRS9mZuLkgaZGPyUs="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.rtl.css"}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.cg64932msi.css.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"v3pwxTmdp3AUJ0GA86s+grW/VNd2eY0WdotmZhVLaEA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cg64932msi"}, {"Name": "integrity", "Value": "sha256-v3pwxTmdp3AUJ0GA86s+grW/VNd2eY0WdotmZhVLaEA="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.rtl.css.gz"}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.css", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000294550810"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"v3pwxTmdp3AUJ0GA86s+grW/VNd2eY0WdotmZhVLaEA=\""}, {"Name": "ETag", "Value": "W/\"VNG6ooQa6SL8Cut+eozIV8WqAMsRS9mZuLkgaZGPyUs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VNG6ooQa6SL8Cut+eozIV8WqAMsRS9mZuLkgaZGPyUs="}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.css", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12651"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VNG6ooQa6SL8Cut+eozIV8WqAMsRS9mZuLkgaZGPyUs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VNG6ooQa6SL8Cut+eozIV8WqAMsRS9mZuLkgaZGPyUs="}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.css.4rhpehgfce.map", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038719170"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25826"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3Rvvf7W5tqLI21j9/j3Ncw943bK3QaHnDGHdSSG3Zcg=\""}, {"Name": "ETag", "Value": "W/\"4GfXViFA1qaqZrWRvGe0sh+TsJfjpO6Yrd38IQ4+4bs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4rhpehgfce"}, {"Name": "integrity", "Value": "sha256-4GfXViFA1qaqZrWRvGe0sh+TsJfjpO6Yrd38IQ4+4bs="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.rtl.css.map"}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.css.4rhpehgfce.map", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "129580"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4GfXViFA1qaqZrWRvGe0sh+TsJfjpO6Yrd38IQ4+4bs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4rhpehgfce"}, {"Name": "integrity", "Value": "sha256-4GfXViFA1qaqZrWRvGe0sh+TsJfjpO6Yrd38IQ4+4bs="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.rtl.css.map"}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.css.4rhpehgfce.map.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25826"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3Rvvf7W5tqLI21j9/j3Ncw943bK3QaHnDGHdSSG3Zcg=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4rhpehgfce"}, {"Name": "integrity", "Value": "sha256-3Rvvf7W5tqLI21j9/j3Ncw943bK3QaHnDGHdSSG3Zcg="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.rtl.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.css.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"v3pwxTmdp3AUJ0GA86s+grW/VNd2eY0WdotmZhVLaEA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v3pwxTmdp3AUJ0GA86s+grW/VNd2eY0WdotmZhVLaEA="}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.css.map", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038719170"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25826"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3Rvvf7W5tqLI21j9/j3Ncw943bK3QaHnDGHdSSG3Zcg=\""}, {"Name": "ETag", "Value": "W/\"4GfXViFA1qaqZrWRvGe0sh+TsJfjpO6Yrd38IQ4+4bs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4GfXViFA1qaqZrWRvGe0sh+TsJfjpO6Yrd38IQ4+4bs="}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.css.map", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "129580"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4GfXViFA1qaqZrWRvGe0sh+TsJfjpO6Yrd38IQ4+4bs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4GfXViFA1qaqZrWRvGe0sh+TsJfjpO6Yrd38IQ4+4bs="}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.css.map.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25826"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3Rvvf7W5tqLI21j9/j3Ncw943bK3QaHnDGHdSSG3Zcg=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3Rvvf7W5tqLI21j9/j3Ncw943bK3QaHnDGHdSSG3Zcg="}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.min.css", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000307692308"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3249"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CaBQMj2Lofe2QHG4pCuENtS7IfTv9kdyUB/s0Wf+xHI=\""}, {"Name": "ETag", "Value": "W/\"M0jKffa8tj3TvKrDoJw6X/D/romGyeh5oSCCM3rTyJs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M0jKffa8tj3TvKrDoJw6X/D/romGyeh5oSCCM3rTyJs="}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.min.css", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10203"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"M0jKffa8tj3TvKrDoJw6X/D/romGyeh5oSCCM3rTyJs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M0jKffa8tj3TvKrDoJw6X/D/romGyeh5oSCCM3rTyJs="}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.min.css.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3249"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CaBQMj2Lofe2QHG4pCuENtS7IfTv9kdyUB/s0Wf+xHI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CaBQMj2Lofe2QHG4pCuENtS7IfTv9kdyUB/s0Wf+xHI="}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066484941"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15040"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bamrMxU+rQuXvXyqQsJRuQtWLy/PQYC4YG+ayso0Vx0=\""}, {"Name": "ETag", "Value": "W/\"4D7TyWGhoJ89MFZd8hoxz01YecGnICsTnVnbQuhXpRI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4D7TyWGhoJ89MFZd8hoxz01YecGnICsTnVnbQuhXpRI="}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "63942"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4D7TyWGhoJ89MFZd8hoxz01YecGnICsTnVnbQuhXpRI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4D7TyWGhoJ89MFZd8hoxz01YecGnICsTnVnbQuhXpRI="}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15040"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bamrMxU+rQuXvXyqQsJRuQtWLy/PQYC4YG+ayso0Vx0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bamrMxU+rQuXvXyqQsJRuQtWLy/PQYC4YG+ayso0Vx0="}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.min.css.sdzno1bzos.map", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066484941"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15040"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bamrMxU+rQuXvXyqQsJRuQtWLy/PQYC4YG+ayso0Vx0=\""}, {"Name": "ETag", "Value": "W/\"4D7TyWGhoJ89MFZd8hoxz01YecGnICsTnVnbQuhXpRI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sdzno1bzos"}, {"Name": "integrity", "Value": "sha256-4D7TyWGhoJ89MFZd8hoxz01YecGnICsTnVnbQuhXpRI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.rtl.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.min.css.sdzno1bzos.map", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "63942"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4D7TyWGhoJ89MFZd8hoxz01YecGnICsTnVnbQuhXpRI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sdzno1bzos"}, {"Name": "integrity", "Value": "sha256-4D7TyWGhoJ89MFZd8hoxz01YecGnICsTnVnbQuhXpRI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.rtl.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.min.css.sdzno1bzos.map.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15040"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bamrMxU+rQuXvXyqQsJRuQtWLy/PQYC4YG+ayso0Vx0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sdzno1bzos"}, {"Name": "integrity", "Value": "sha256-bamrMxU+rQuXvXyqQsJRuQtWLy/PQYC4YG+ayso0Vx0="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.rtl.min.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.min.douy0e3bu8.css", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000307692308"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3249"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CaBQMj2Lofe2QHG4pCuENtS7IfTv9kdyUB/s0Wf+xHI=\""}, {"Name": "ETag", "Value": "W/\"M0jKffa8tj3TvKrDoJw6X/D/romGyeh5oSCCM3rTyJs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "douy0e3bu8"}, {"Name": "integrity", "Value": "sha256-M0jKffa8tj3TvKrDoJw6X/D/romGyeh5oSCCM3rTyJs="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.rtl.min.css"}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.min.douy0e3bu8.css", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10203"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"M0jKffa8tj3TvKrDoJw6X/D/romGyeh5oSCCM3rTyJs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "douy0e3bu8"}, {"Name": "integrity", "Value": "sha256-M0jKffa8tj3TvKrDoJw6X/D/romGyeh5oSCCM3rTyJs="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.rtl.min.css"}]}, {"Route": "bootstrap/css/bootstrap-reboot.rtl.min.douy0e3bu8.css.gz", "AssetFile": "bootstrap/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3249"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CaBQMj2Lofe2QHG4pCuENtS7IfTv9kdyUB/s0Wf+xHI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "douy0e3bu8"}, {"Name": "integrity", "Value": "sha256-CaBQMj2Lofe2QHG4pCuENtS7IfTv9kdyUB/s0Wf+xHI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-reboot.rtl.min.css.gz"}]}, {"Route": "bootstrap/css/bootstrap-utilities.css", "AssetFile": "bootstrap/css/bootstrap-utilities.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000082767754"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12081"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YBMT3t/Q97SDQ+2LKvSAh5yZ9TBPq++GfzW76VV6Vx8=\""}, {"Name": "ETag", "Value": "W/\"ysDWv7MM8ZItxDAJrvG9fDt7H4IFreW3AZqy1F9NWSs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ysDWv7MM8ZItxDAJrvG9fDt7H4IFreW3AZqy1F9NWSs="}]}, {"Route": "bootstrap/css/bootstrap-utilities.css", "AssetFile": "bootstrap/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "113224"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ysDWv7MM8ZItxDAJrvG9fDt7H4IFreW3AZqy1F9NWSs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ysDWv7MM8ZItxDAJrvG9fDt7H4IFreW3AZqy1F9NWSs="}]}, {"Route": "bootstrap/css/bootstrap-utilities.css.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12081"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YBMT3t/Q97SDQ+2LKvSAh5yZ9TBPq++GfzW76VV6Vx8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YBMT3t/Q97SDQ+2LKvSAh5yZ9TBPq++GfzW76VV6Vx8="}]}, {"Route": "bootstrap/css/bootstrap-utilities.css.kobcaiybo0.map", "AssetFile": "bootstrap/css/bootstrap-utilities.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022664430"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44121"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NscEhEegYjDvIdFiTwIfc51vz2+aIoPtA4x3oL49dhs=\""}, {"Name": "ETag", "Value": "W/\"JHht7OXL/C9v82nlNSpAG2yIQTqIvt9aoHXYap4aKT4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kobcaiybo0"}, {"Name": "integrity", "Value": "sha256-JHht7OXL/C9v82nlNSpAG2yIQTqIvt9aoHXYap4aKT4="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.css.map"}]}, {"Route": "bootstrap/css/bootstrap-utilities.css.kobcaiybo0.map", "AssetFile": "bootstrap/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "267728"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"JHht7OXL/C9v82nlNSpAG2yIQTqIvt9aoHXYap4aKT4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kobcaiybo0"}, {"Name": "integrity", "Value": "sha256-JHht7OXL/C9v82nlNSpAG2yIQTqIvt9aoHXYap4aKT4="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.css.map"}]}, {"Route": "bootstrap/css/bootstrap-utilities.css.kobcaiybo0.map.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44121"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NscEhEegYjDvIdFiTwIfc51vz2+aIoPtA4x3oL49dhs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kobcaiybo0"}, {"Name": "integrity", "Value": "sha256-NscEhEegYjDvIdFiTwIfc51vz2+aIoPtA4x3oL49dhs="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap-utilities.css.map", "AssetFile": "bootstrap/css/bootstrap-utilities.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022664430"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44121"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NscEhEegYjDvIdFiTwIfc51vz2+aIoPtA4x3oL49dhs=\""}, {"Name": "ETag", "Value": "W/\"JHht7OXL/C9v82nlNSpAG2yIQTqIvt9aoHXYap4aKT4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JHht7OXL/C9v82nlNSpAG2yIQTqIvt9aoHXYap4aKT4="}]}, {"Route": "bootstrap/css/bootstrap-utilities.css.map", "AssetFile": "bootstrap/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "267728"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"JHht7OXL/C9v82nlNSpAG2yIQTqIvt9aoHXYap4aKT4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JHht7OXL/C9v82nlNSpAG2yIQTqIvt9aoHXYap4aKT4="}]}, {"Route": "bootstrap/css/bootstrap-utilities.css.map.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44121"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NscEhEegYjDvIdFiTwIfc51vz2+aIoPtA4x3oL49dhs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NscEhEegYjDvIdFiTwIfc51vz2+aIoPtA4x3oL49dhs="}]}, {"Route": "bootstrap/css/bootstrap-utilities.ia1x4yblij.css", "AssetFile": "bootstrap/css/bootstrap-utilities.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000082767754"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12081"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YBMT3t/Q97SDQ+2LKvSAh5yZ9TBPq++GfzW76VV6Vx8=\""}, {"Name": "ETag", "Value": "W/\"ysDWv7MM8ZItxDAJrvG9fDt7H4IFreW3AZqy1F9NWSs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ia1x4yblij"}, {"Name": "integrity", "Value": "sha256-ysDWv7MM8ZItxDAJrvG9fDt7H4IFreW3AZqy1F9NWSs="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.css"}]}, {"Route": "bootstrap/css/bootstrap-utilities.ia1x4yblij.css", "AssetFile": "bootstrap/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "113224"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ysDWv7MM8ZItxDAJrvG9fDt7H4IFreW3AZqy1F9NWSs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ia1x4yblij"}, {"Name": "integrity", "Value": "sha256-ysDWv7MM8ZItxDAJrvG9fDt7H4IFreW3AZqy1F9NWSs="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.css"}]}, {"Route": "bootstrap/css/bootstrap-utilities.ia1x4yblij.css.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12081"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YBMT3t/Q97SDQ+2LKvSAh5yZ9TBPq++GfzW76VV6Vx8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ia1x4yblij"}, {"Name": "integrity", "Value": "sha256-YBMT3t/Q97SDQ+2LKvSAh5yZ9TBPq++GfzW76VV6Vx8="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.css.gz"}]}, {"Route": "bootstrap/css/bootstrap-utilities.min.1ib6psjuhl.css", "AssetFile": "bootstrap/css/bootstrap-utilities.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090350560"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11067"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EIItrmG+Fi829jmyS5izDiIdKr9fdV085DbAFJS525Q=\""}, {"Name": "ETag", "Value": "W/\"udiNlxfiI61vwJVY7K8oVuhF12s3yEElhD9vw4qXwxM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1ib6psjuhl"}, {"Name": "integrity", "Value": "sha256-udiNlxfiI61vwJVY7K8oVuhF12s3yEElhD9vw4qXwxM="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.min.css"}]}, {"Route": "bootstrap/css/bootstrap-utilities.min.1ib6psjuhl.css", "AssetFile": "bootstrap/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "85357"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"udiNlxfiI61vwJVY7K8oVuhF12s3yEElhD9vw4qXwxM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1ib6psjuhl"}, {"Name": "integrity", "Value": "sha256-udiNlxfiI61vwJVY7K8oVuhF12s3yEElhD9vw4qXwxM="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.min.css"}]}, {"Route": "bootstrap/css/bootstrap-utilities.min.1ib6psjuhl.css.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11067"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EIItrmG+Fi829jmyS5izDiIdKr9fdV085DbAFJS525Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1ib6psjuhl"}, {"Name": "integrity", "Value": "sha256-EIItrmG+Fi829jmyS5izDiIdKr9fdV085DbAFJS525Q="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.min.css.gz"}]}, {"Route": "bootstrap/css/bootstrap-utilities.min.css", "AssetFile": "bootstrap/css/bootstrap-utilities.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090350560"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11067"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EIItrmG+Fi829jmyS5izDiIdKr9fdV085DbAFJS525Q=\""}, {"Name": "ETag", "Value": "W/\"udiNlxfiI61vwJVY7K8oVuhF12s3yEElhD9vw4qXwxM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-udiNlxfiI61vwJVY7K8oVuhF12s3yEElhD9vw4qXwxM="}]}, {"Route": "bootstrap/css/bootstrap-utilities.min.css", "AssetFile": "bootstrap/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "85357"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"udiNlxfiI61vwJVY7K8oVuhF12s3yEElhD9vw4qXwxM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-udiNlxfiI61vwJVY7K8oVuhF12s3yEElhD9vw4qXwxM="}]}, {"Route": "bootstrap/css/bootstrap-utilities.min.css.5sxm1litgd.map", "AssetFile": "bootstrap/css/bootstrap-utilities.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041082946"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24340"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EYuSc2xNvXefSvBssxQWxNro72coap6Ii1E20wyrSqA=\""}, {"Name": "ETag", "Value": "W/\"ezbG2UZwe3qELsjzv6CzNLxxFgpBOS6bSb4pOr8SaC8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5sxm1litgd"}, {"Name": "integrity", "Value": "sha256-ezbG2UZwe3qELsjzv6CzNLxxFgpBOS6bSb4pOr8SaC8="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap-utilities.min.css.5sxm1litgd.map", "AssetFile": "bootstrap/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ezbG2UZwe3qELsjzv6CzNLxxFgpBOS6bSb4pOr8SaC8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5sxm1litgd"}, {"Name": "integrity", "Value": "sha256-ezbG2UZwe3qELsjzv6CzNLxxFgpBOS6bSb4pOr8SaC8="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap-utilities.min.css.5sxm1litgd.map.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24340"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EYuSc2xNvXefSvBssxQWxNro72coap6Ii1E20wyrSqA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5sxm1litgd"}, {"Name": "integrity", "Value": "sha256-EYuSc2xNvXefSvBssxQWxNro72coap6Ii1E20wyrSqA="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.min.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap-utilities.min.css.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11067"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EIItrmG+Fi829jmyS5izDiIdKr9fdV085DbAFJS525Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EIItrmG+Fi829jmyS5izDiIdKr9fdV085DbAFJS525Q="}]}, {"Route": "bootstrap/css/bootstrap-utilities.min.css.map", "AssetFile": "bootstrap/css/bootstrap-utilities.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041082946"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24340"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EYuSc2xNvXefSvBssxQWxNro72coap6Ii1E20wyrSqA=\""}, {"Name": "ETag", "Value": "W/\"ezbG2UZwe3qELsjzv6CzNLxxFgpBOS6bSb4pOr8SaC8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ezbG2UZwe3qELsjzv6CzNLxxFgpBOS6bSb4pOr8SaC8="}]}, {"Route": "bootstrap/css/bootstrap-utilities.min.css.map", "AssetFile": "bootstrap/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ezbG2UZwe3qELsjzv6CzNLxxFgpBOS6bSb4pOr8SaC8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ezbG2UZwe3qELsjzv6CzNLxxFgpBOS6bSb4pOr8SaC8="}]}, {"Route": "bootstrap/css/bootstrap-utilities.min.css.map.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24340"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EYuSc2xNvXefSvBssxQWxNro72coap6Ii1E20wyrSqA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EYuSc2xNvXefSvBssxQWxNro72coap6Ii1E20wyrSqA="}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.6wcxerfyve.css", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083097889"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12033"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oNOketZI1n1A4+YO4lUb8IMbE3egfRakjiZikG8o7b4=\""}, {"Name": "ETag", "Value": "W/\"hkn7Jpf1TLVdoNsOtJ+3cpoh7psbdOQUhi6it7Y9tpU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6wcxerfyve"}, {"Name": "integrity", "Value": "sha256-hkn7Jpf1TLVdoNsOtJ+3cpoh7psbdOQUhi6it7Y9tpU="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.rtl.css"}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.6wcxerfyve.css", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "113083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hkn7Jpf1TLVdoNsOtJ+3cpoh7psbdOQUhi6it7Y9tpU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6wcxerfyve"}, {"Name": "integrity", "Value": "sha256-hkn7Jpf1TLVdoNsOtJ+3cpoh7psbdOQUhi6it7Y9tpU="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.rtl.css"}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.6wcxerfyve.css.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12033"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oNOketZI1n1A4+YO4lUb8IMbE3egfRakjiZikG8o7b4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6wcxerfyve"}, {"Name": "integrity", "Value": "sha256-oNOketZI1n1A4+YO4lUb8IMbE3egfRakjiZikG8o7b4="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.rtl.css.gz"}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.css", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083097889"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12033"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oNOketZI1n1A4+YO4lUb8IMbE3egfRakjiZikG8o7b4=\""}, {"Name": "ETag", "Value": "W/\"hkn7Jpf1TLVdoNsOtJ+3cpoh7psbdOQUhi6it7Y9tpU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hkn7Jpf1TLVdoNsOtJ+3cpoh7psbdOQUhi6it7Y9tpU="}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.css", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "113083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hkn7Jpf1TLVdoNsOtJ+3cpoh7psbdOQUhi6it7Y9tpU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hkn7Jpf1TLVdoNsOtJ+3cpoh7psbdOQUhi6it7Y9tpU="}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.css.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12033"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oNOketZI1n1A4+YO4lUb8IMbE3egfRakjiZikG8o7b4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oNOketZI1n1A4+YO4lUb8IMbE3egfRakjiZikG8o7b4="}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.css.map", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022678308"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44094"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kcioHnQfYUsOykNWOR0cxUIMZymAt7w8ueKi0ueTTZc=\""}, {"Name": "ETag", "Value": "W/\"Kvb+wq581qyogiqcerkKWHAooTcGoQeWoW2QpzWiiTE=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kvb+wq581qyogiqcerkKWHAooTcGoQeWoW2QpzWiiTE="}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.css.map", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "267669"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kvb+wq581qyogiqcerkKWHAooTcGoQeWoW2QpzWiiTE=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kvb+wq581qyogiqcerkKWHAooTcGoQeWoW2QpzWiiTE="}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.css.map.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44094"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kcioHnQfYUsOykNWOR0cxUIMZymAt7w8ueKi0ueTTZc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kcioHnQfYUsOykNWOR0cxUIMZymAt7w8ueKi0ueTTZc="}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.css.uyvkvzc6pw.map", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022678308"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44094"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kcioHnQfYUsOykNWOR0cxUIMZymAt7w8ueKi0ueTTZc=\""}, {"Name": "ETag", "Value": "W/\"Kvb+wq581qyogiqcerkKWHAooTcGoQeWoW2QpzWiiTE=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uyvkvzc6pw"}, {"Name": "integrity", "Value": "sha256-Kvb+wq581qyogiqcerkKWHAooTcGoQeWoW2QpzWiiTE="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.rtl.css.map"}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.css.uyvkvzc6pw.map", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "267669"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kvb+wq581qyogiqcerkKWHAooTcGoQeWoW2QpzWiiTE=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uyvkvzc6pw"}, {"Name": "integrity", "Value": "sha256-Kvb+wq581qyogiqcerkKWHAooTcGoQeWoW2QpzWiiTE="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.rtl.css.map"}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.css.uyvkvzc6pw.map.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44094"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kcioHnQfYUsOykNWOR0cxUIMZymAt7w8ueKi0ueTTZc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uyvkvzc6pw"}, {"Name": "integrity", "Value": "sha256-kcioHnQfYUsOykNWOR0cxUIMZymAt7w8ueKi0ueTTZc="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.rtl.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.min.9c9pbw9g7a.css", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090489548"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+8bVWAltbPVbwnEVKMW3dEnVRrYkAHxhWl4lFY3i2Tw=\""}, {"Name": "ETag", "Value": "W/\"ifZKcRC9lLwRRx4xFirFnLLQe5evzfWDV8sai6L7o4k=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9c9pbw9g7a"}, {"Name": "integrity", "Value": "sha256-ifZKcRC9lLwRRx4xFirFnLLQe5evzfWDV8sai6L7o4k="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.rtl.min.css"}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.min.9c9pbw9g7a.css", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "85286"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ifZKcRC9lLwRRx4xFirFnLLQe5evzfWDV8sai6L7o4k=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9c9pbw9g7a"}, {"Name": "integrity", "Value": "sha256-ifZKcRC9lLwRRx4xFirFnLLQe5evzfWDV8sai6L7o4k="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.rtl.min.css"}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.min.9c9pbw9g7a.css.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+8bVWAltbPVbwnEVKMW3dEnVRrYkAHxhWl4lFY3i2Tw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9c9pbw9g7a"}, {"Name": "integrity", "Value": "sha256-+8bVWAltbPVbwnEVKMW3dEnVRrYkAHxhWl4lFY3i2Tw="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.rtl.min.css.gz"}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.min.css", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090489548"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+8bVWAltbPVbwnEVKMW3dEnVRrYkAHxhWl4lFY3i2Tw=\""}, {"Name": "ETag", "Value": "W/\"ifZKcRC9lLwRRx4xFirFnLLQe5evzfWDV8sai6L7o4k=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ifZKcRC9lLwRRx4xFirFnLLQe5evzfWDV8sai6L7o4k="}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.min.css", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "85286"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ifZKcRC9lLwRRx4xFirFnLLQe5evzfWDV8sai6L7o4k=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ifZKcRC9lLwRRx4xFirFnLLQe5evzfWDV8sai6L7o4k="}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.min.css.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+8bVWAltbPVbwnEVKMW3dEnVRrYkAHxhWl4lFY3i2Tw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+8bVWAltbPVbwnEVKMW3dEnVRrYkAHxhWl4lFY3i2Tw="}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041164121"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24292"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8FyyyLsVH21VoP8IU61/n6NebQYUilpKmFqDXf8I68Q=\""}, {"Name": "ETag", "Value": "W/\"iYVl07J1fPD5PDc/Hz3firNzEtg87mE5rUvXjsse5zU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iYVl07J1fPD5PDc/Hz3firNzEtg87mE5rUvXjsse5zU="}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"iYVl07J1fPD5PDc/Hz3firNzEtg87mE5rUvXjsse5zU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iYVl07J1fPD5PDc/Hz3firNzEtg87mE5rUvXjsse5zU="}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24292"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8FyyyLsVH21VoP8IU61/n6NebQYUilpKmFqDXf8I68Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8FyyyLsVH21VoP8IU61/n6NebQYUilpKmFqDXf8I68Q="}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.min.css.nowsy9otc3.map", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041164121"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24292"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8FyyyLsVH21VoP8IU61/n6NebQYUilpKmFqDXf8I68Q=\""}, {"Name": "ETag", "Value": "W/\"iYVl07J1fPD5PDc/Hz3firNzEtg87mE5rUvXjsse5zU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nowsy9otc3"}, {"Name": "integrity", "Value": "sha256-iYVl07J1fPD5PDc/Hz3firNzEtg87mE5rUvXjsse5zU="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.rtl.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.min.css.nowsy9otc3.map", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"iYVl07J1fPD5PDc/Hz3firNzEtg87mE5rUvXjsse5zU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nowsy9otc3"}, {"Name": "integrity", "Value": "sha256-iYVl07J1fPD5PDc/Hz3firNzEtg87mE5rUvXjsse5zU="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.rtl.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap-utilities.rtl.min.css.nowsy9otc3.map.gz", "AssetFile": "bootstrap/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24292"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8FyyyLsVH21VoP8IU61/n6NebQYUilpKmFqDXf8I68Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nowsy9otc3"}, {"Name": "integrity", "Value": "sha256-8FyyyLsVH21VoP8IU61/n6NebQYUilpKmFqDXf8I68Q="}, {"Name": "label", "Value": "bootstrap/css/bootstrap-utilities.rtl.min.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap.css", "AssetFile": "bootstrap/css/bootstrap.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029864118"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33484"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"g4stU6R6QAcIzRUtPX1oEwnDJ+mi5dTSGAsTndCpKCY=\""}, {"Name": "ETag", "Value": "W/\"8+y+JMP1TbUveMxl1SU/Lns5RaeKt5L/zToerNbAbfs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8+y+JMP1TbUveMxl1SU/Lns5RaeKt5L/zToerNbAbfs="}]}, {"Route": "bootstrap/css/bootstrap.css", "AssetFile": "bootstrap/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "292109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8+y+JMP1TbUveMxl1SU/Lns5RaeKt5L/zToerNbAbfs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8+y+JMP1TbUveMxl1SU/Lns5RaeKt5L/zToerNbAbfs="}]}, {"Route": "bootstrap/css/bootstrap.css.2w2h86zmsh.map", "AssetFile": "bootstrap/css/bootstrap.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008699359"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114950"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"zftyI4hW02k9H4838tEM3NVE8xB7M4T1grkm911BTuc=\""}, {"Name": "ETag", "Value": "W/\"Hkeuu9W8fni63S4StpivYSirr9ErFR7UFqb5ueW1hTI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2w2h86zmsh"}, {"Name": "integrity", "Value": "sha256-Hkeuu9W8fni63S4StpivYSirr9ErFR7UFqb5ueW1hTI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.css.map"}]}, {"Route": "bootstrap/css/bootstrap.css.2w2h86zmsh.map", "AssetFile": "bootstrap/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "680302"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Hkeuu9W8fni63S4StpivYSirr9ErFR7UFqb5ueW1hTI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2w2h86zmsh"}, {"Name": "integrity", "Value": "sha256-Hkeuu9W8fni63S4StpivYSirr9ErFR7UFqb5ueW1hTI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.css.map"}]}, {"Route": "bootstrap/css/bootstrap.css.2w2h86zmsh.map.gz", "AssetFile": "bootstrap/css/bootstrap.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114950"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"zftyI4hW02k9H4838tEM3NVE8xB7M4T1grkm911BTuc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2w2h86zmsh"}, {"Name": "integrity", "Value": "sha256-zftyI4hW02k9H4838tEM3NVE8xB7M4T1grkm911BTuc="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap.css.gz", "AssetFile": "bootstrap/css/bootstrap.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33484"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"g4stU6R6QAcIzRUtPX1oEwnDJ+mi5dTSGAsTndCpKCY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g4stU6R6QAcIzRUtPX1oEwnDJ+mi5dTSGAsTndCpKCY="}]}, {"Route": "bootstrap/css/bootstrap.css.map", "AssetFile": "bootstrap/css/bootstrap.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008699359"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114950"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"zftyI4hW02k9H4838tEM3NVE8xB7M4T1grkm911BTuc=\""}, {"Name": "ETag", "Value": "W/\"Hkeuu9W8fni63S4StpivYSirr9ErFR7UFqb5ueW1hTI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hkeuu9W8fni63S4StpivYSirr9ErFR7UFqb5ueW1hTI="}]}, {"Route": "bootstrap/css/bootstrap.css.map", "AssetFile": "bootstrap/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "680302"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Hkeuu9W8fni63S4StpivYSirr9ErFR7UFqb5ueW1hTI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hkeuu9W8fni63S4StpivYSirr9ErFR7UFqb5ueW1hTI="}]}, {"Route": "bootstrap/css/bootstrap.css.map.gz", "AssetFile": "bootstrap/css/bootstrap.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114950"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"zftyI4hW02k9H4838tEM3NVE8xB7M4T1grkm911BTuc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zftyI4hW02k9H4838tEM3NVE8xB7M4T1grkm911BTuc="}]}, {"Route": "bootstrap/css/bootstrap.min.8js49wmkp1.css", "AssetFile": "bootstrap/css/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032325845"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30934"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"p9nRE8KCl3gkVJDTTltupiuaA7wQhGo8mzFE6iQkHb4=\""}, {"Name": "ETag", "Value": "W/\"jA1cmG3prCzT7UKH3upCdZSMRRhr+2W/IQtDLeI4o9M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8js49wmkp1"}, {"Name": "integrity", "Value": "sha256-jA1cmG3prCzT7UKH3upCdZSMRRhr+2W/IQtDLeI4o9M="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.min.css"}]}, {"Route": "bootstrap/css/bootstrap.min.8js49wmkp1.css", "AssetFile": "bootstrap/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "231918"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jA1cmG3prCzT7UKH3upCdZSMRRhr+2W/IQtDLeI4o9M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8js49wmkp1"}, {"Name": "integrity", "Value": "sha256-jA1cmG3prCzT7UKH3upCdZSMRRhr+2W/IQtDLeI4o9M="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.min.css"}]}, {"Route": "bootstrap/css/bootstrap.min.8js49wmkp1.css.gz", "AssetFile": "bootstrap/css/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30934"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"p9nRE8KCl3gkVJDTTltupiuaA7wQhGo8mzFE6iQkHb4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8js49wmkp1"}, {"Name": "integrity", "Value": "sha256-p9nRE8KCl3gkVJDTTltupiuaA7wQhGo8mzFE6iQkHb4="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.min.css.gz"}]}, {"Route": "bootstrap/css/bootstrap.min.css", "AssetFile": "bootstrap/css/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032325845"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30934"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"p9nRE8KCl3gkVJDTTltupiuaA7wQhGo8mzFE6iQkHb4=\""}, {"Name": "ETag", "Value": "W/\"jA1cmG3prCzT7UKH3upCdZSMRRhr+2W/IQtDLeI4o9M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jA1cmG3prCzT7UKH3upCdZSMRRhr+2W/IQtDLeI4o9M="}]}, {"Route": "bootstrap/css/bootstrap.min.css", "AssetFile": "bootstrap/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "231918"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jA1cmG3prCzT7UKH3upCdZSMRRhr+2W/IQtDLeI4o9M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jA1cmG3prCzT7UKH3upCdZSMRRhr+2W/IQtDLeI4o9M="}]}, {"Route": "bootstrap/css/bootstrap.min.css.gz", "AssetFile": "bootstrap/css/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30934"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"p9nRE8KCl3gkVJDTTltupiuaA7wQhGo8mzFE6iQkHb4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p9nRE8KCl3gkVJDTTltupiuaA7wQhGo8mzFE6iQkHb4="}]}, {"Route": "bootstrap/css/bootstrap.min.css.hy1c2auhqz.map", "AssetFile": "bootstrap/css/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010904888"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91701"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"QVrMmKotSWa4vwOKFpxXF2KfXASNJLfsvge+d+hhpU4=\""}, {"Name": "ETag", "Value": "W/\"6V4ilQ0Hy45mqA89dEbzxeQcuTG7beY0qqO4tnKqd44=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hy1c2auhqz"}, {"Name": "integrity", "Value": "sha256-6V4ilQ0Hy45mqA89dEbzxeQcuTG7beY0qqO4tnKqd44="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap.min.css.hy1c2auhqz.map", "AssetFile": "bootstrap/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "589407"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"6V4ilQ0Hy45mqA89dEbzxeQcuTG7beY0qqO4tnKqd44=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hy1c2auhqz"}, {"Name": "integrity", "Value": "sha256-6V4ilQ0Hy45mqA89dEbzxeQcuTG7beY0qqO4tnKqd44="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap.min.css.hy1c2auhqz.map.gz", "AssetFile": "bootstrap/css/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91701"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"QVrMmKotSWa4vwOKFpxXF2KfXASNJLfsvge+d+hhpU4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hy1c2auhqz"}, {"Name": "integrity", "Value": "sha256-QVrMmKotSWa4vwOKFpxXF2KfXASNJLfsvge+d+hhpU4="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.min.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap.min.css.map", "AssetFile": "bootstrap/css/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010904888"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91701"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"QVrMmKotSWa4vwOKFpxXF2KfXASNJLfsvge+d+hhpU4=\""}, {"Name": "ETag", "Value": "W/\"6V4ilQ0Hy45mqA89dEbzxeQcuTG7beY0qqO4tnKqd44=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6V4ilQ0Hy45mqA89dEbzxeQcuTG7beY0qqO4tnKqd44="}]}, {"Route": "bootstrap/css/bootstrap.min.css.map", "AssetFile": "bootstrap/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "589407"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"6V4ilQ0Hy45mqA89dEbzxeQcuTG7beY0qqO4tnKqd44=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6V4ilQ0Hy45mqA89dEbzxeQcuTG7beY0qqO4tnKqd44="}]}, {"Route": "bootstrap/css/bootstrap.min.css.map.gz", "AssetFile": "bootstrap/css/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91701"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"QVrMmKotSWa4vwOKFpxXF2KfXASNJLfsvge+d+hhpU4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QVrMmKotSWa4vwOKFpxXF2KfXASNJLfsvge+d+hhpU4="}]}, {"Route": "bootstrap/css/bootstrap.rtl.css", "AssetFile": "bootstrap/css/bootstrap.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029987705"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33346"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"km+PFZx8TawNRt9MqbGM0rQAl75/Uxrl3Pu6ww4Uxb4=\""}, {"Name": "ETag", "Value": "W/\"tOrXc/TZLJVN6DqqK+SZXtUlHh8adgpVMQKyqP115z8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tOrXc/TZLJVN6DqqK+SZXtUlHh8adgpVMQKyqP115z8="}]}, {"Route": "bootstrap/css/bootstrap.rtl.css", "AssetFile": "bootstrap/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "291297"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tOrXc/TZLJVN6DqqK+SZXtUlHh8adgpVMQKyqP115z8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tOrXc/TZLJVN6DqqK+SZXtUlHh8adgpVMQKyqP115z8="}]}, {"Route": "bootstrap/css/bootstrap.rtl.css.az13er2kcb.map", "AssetFile": "bootstrap/css/bootstrap.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008702993"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114902"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"a1wPsW2RL9JZy2NACwfMU9SHZxZn4h3RgWK1CSUxFGM=\""}, {"Name": "ETag", "Value": "W/\"xmb7pSURSgc1b7HPoVVwEwKrfy8A9CY/c1uUNd0TSVI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "az13er2kcb"}, {"Name": "integrity", "Value": "sha256-xmb7pSURSgc1b7HPoVVwEwKrfy8A9CY/c1uUNd0TSVI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.rtl.css.map"}]}, {"Route": "bootstrap/css/bootstrap.rtl.css.az13er2kcb.map", "AssetFile": "bootstrap/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "680162"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xmb7pSURSgc1b7HPoVVwEwKrfy8A9CY/c1uUNd0TSVI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "az13er2kcb"}, {"Name": "integrity", "Value": "sha256-xmb7pSURSgc1b7HPoVVwEwKrfy8A9CY/c1uUNd0TSVI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.rtl.css.map"}]}, {"Route": "bootstrap/css/bootstrap.rtl.css.az13er2kcb.map.gz", "AssetFile": "bootstrap/css/bootstrap.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114902"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"a1wPsW2RL9JZy2NACwfMU9SHZxZn4h3RgWK1CSUxFGM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "az13er2kcb"}, {"Name": "integrity", "Value": "sha256-a1wPsW2RL9JZy2NACwfMU9SHZxZn4h3RgWK1CSUxFGM="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.rtl.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap.rtl.css.gz", "AssetFile": "bootstrap/css/bootstrap.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33346"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"km+PFZx8TawNRt9MqbGM0rQAl75/Uxrl3Pu6ww4Uxb4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-km+PFZx8TawNRt9MqbGM0rQAl75/Uxrl3Pu6ww4Uxb4="}]}, {"Route": "bootstrap/css/bootstrap.rtl.css.map", "AssetFile": "bootstrap/css/bootstrap.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008702993"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114902"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"a1wPsW2RL9JZy2NACwfMU9SHZxZn4h3RgWK1CSUxFGM=\""}, {"Name": "ETag", "Value": "W/\"xmb7pSURSgc1b7HPoVVwEwKrfy8A9CY/c1uUNd0TSVI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xmb7pSURSgc1b7HPoVVwEwKrfy8A9CY/c1uUNd0TSVI="}]}, {"Route": "bootstrap/css/bootstrap.rtl.css.map", "AssetFile": "bootstrap/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "680162"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xmb7pSURSgc1b7HPoVVwEwKrfy8A9CY/c1uUNd0TSVI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xmb7pSURSgc1b7HPoVVwEwKrfy8A9CY/c1uUNd0TSVI="}]}, {"Route": "bootstrap/css/bootstrap.rtl.css.map.gz", "AssetFile": "bootstrap/css/bootstrap.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114902"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"a1wPsW2RL9JZy2NACwfMU9SHZxZn4h3RgWK1CSUxFGM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1wPsW2RL9JZy2NACwfMU9SHZxZn4h3RgWK1CSUxFGM="}]}, {"Route": "bootstrap/css/bootstrap.rtl.k849h3ts61.css", "AssetFile": "bootstrap/css/bootstrap.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029987705"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33346"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"km+PFZx8TawNRt9MqbGM0rQAl75/Uxrl3Pu6ww4Uxb4=\""}, {"Name": "ETag", "Value": "W/\"tOrXc/TZLJVN6DqqK+SZXtUlHh8adgpVMQKyqP115z8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k849h3ts61"}, {"Name": "integrity", "Value": "sha256-tOrXc/TZLJVN6DqqK+SZXtUlHh8adgpVMQKyqP115z8="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.rtl.css"}]}, {"Route": "bootstrap/css/bootstrap.rtl.k849h3ts61.css", "AssetFile": "bootstrap/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "291297"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tOrXc/TZLJVN6DqqK+SZXtUlHh8adgpVMQKyqP115z8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k849h3ts61"}, {"Name": "integrity", "Value": "sha256-tOrXc/TZLJVN6DqqK+SZXtUlHh8adgpVMQKyqP115z8="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.rtl.css"}]}, {"Route": "bootstrap/css/bootstrap.rtl.k849h3ts61.css.gz", "AssetFile": "bootstrap/css/bootstrap.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33346"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"km+PFZx8TawNRt9MqbGM0rQAl75/Uxrl3Pu6ww4Uxb4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k849h3ts61"}, {"Name": "integrity", "Value": "sha256-km+PFZx8TawNRt9MqbGM0rQAl75/Uxrl3Pu6ww4Uxb4="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.rtl.css.gz"}]}, {"Route": "bootstrap/css/bootstrap.rtl.min.css", "AssetFile": "bootstrap/css/bootstrap.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032306002"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30953"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lgdAeS4cP/OuaRmw5emVPRpxlsabNKuIK4GMUqfNsto=\""}, {"Name": "ETag", "Value": "W/\"yjpGvf/m1/C8zXmeeS0w+5yNaaUQDRA+gZBv2MmgLRQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yjpGvf/m1/C8zXmeeS0w+5yNaaUQDRA+gZBv2MmgLRQ="}]}, {"Route": "bootstrap/css/bootstrap.rtl.min.css", "AssetFile": "bootstrap/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "232026"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"yjpGvf/m1/C8zXmeeS0w+5yNaaUQDRA+gZBv2MmgLRQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yjpGvf/m1/C8zXmeeS0w+5yNaaUQDRA+gZBv2MmgLRQ="}]}, {"Route": "bootstrap/css/bootstrap.rtl.min.css.gz", "AssetFile": "bootstrap/css/bootstrap.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30953"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lgdAeS4cP/OuaRmw5emVPRpxlsabNKuIK4GMUqfNsto=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lgdAeS4cP/OuaRmw5emVPRpxlsabNKuIK4GMUqfNsto="}]}, {"Route": "bootstrap/css/bootstrap.rtl.min.css.map", "AssetFile": "bootstrap/css/bootstrap.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010917150"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91598"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KG7UN19quLqB85JRvGyCMOMgWXOlBvznIA9ACxGMSic=\""}, {"Name": "ETag", "Value": "W/\"OzauJc/y1dBleHEvwrhbp6RLkNycpRtWxrFXVscimqI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OzauJc/y1dBleHEvwrhbp6RLkNycpRtWxrFXVscimqI="}]}, {"Route": "bootstrap/css/bootstrap.rtl.min.css.map", "AssetFile": "bootstrap/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "588604"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OzauJc/y1dBleHEvwrhbp6RLkNycpRtWxrFXVscimqI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OzauJc/y1dBleHEvwrhbp6RLkNycpRtWxrFXVscimqI="}]}, {"Route": "bootstrap/css/bootstrap.rtl.min.css.map.gz", "AssetFile": "bootstrap/css/bootstrap.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91598"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KG7UN19quLqB85JRvGyCMOMgWXOlBvznIA9ACxGMSic=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KG7UN19quLqB85JRvGyCMOMgWXOlBvznIA9ACxGMSic="}]}, {"Route": "bootstrap/css/bootstrap.rtl.min.css.zipk0q9xh7.map", "AssetFile": "bootstrap/css/bootstrap.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010917150"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91598"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KG7UN19quLqB85JRvGyCMOMgWXOlBvznIA9ACxGMSic=\""}, {"Name": "ETag", "Value": "W/\"OzauJc/y1dBleHEvwrhbp6RLkNycpRtWxrFXVscimqI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zipk0q9xh7"}, {"Name": "integrity", "Value": "sha256-OzauJc/y1dBleHEvwrhbp6RLkNycpRtWxrFXVscimqI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.rtl.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap.rtl.min.css.zipk0q9xh7.map", "AssetFile": "bootstrap/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "588604"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OzauJc/y1dBleHEvwrhbp6RLkNycpRtWxrFXVscimqI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zipk0q9xh7"}, {"Name": "integrity", "Value": "sha256-OzauJc/y1dBleHEvwrhbp6RLkNycpRtWxrFXVscimqI="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.rtl.min.css.map"}]}, {"Route": "bootstrap/css/bootstrap.rtl.min.css.zipk0q9xh7.map.gz", "AssetFile": "bootstrap/css/bootstrap.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91598"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KG7UN19quLqB85JRvGyCMOMgWXOlBvznIA9ACxGMSic=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zipk0q9xh7"}, {"Name": "integrity", "Value": "sha256-KG7UN19quLqB85JRvGyCMOMgWXOlBvznIA9ACxGMSic="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.rtl.min.css.map.gz"}]}, {"Route": "bootstrap/css/bootstrap.rtl.min.mkdi438znt.css", "AssetFile": "bootstrap/css/bootstrap.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032306002"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30953"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lgdAeS4cP/OuaRmw5emVPRpxlsabNKuIK4GMUqfNsto=\""}, {"Name": "ETag", "Value": "W/\"yjpGvf/m1/C8zXmeeS0w+5yNaaUQDRA+gZBv2MmgLRQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mkdi438znt"}, {"Name": "integrity", "Value": "sha256-yjpGvf/m1/C8zXmeeS0w+5yNaaUQDRA+gZBv2MmgLRQ="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.rtl.min.css"}]}, {"Route": "bootstrap/css/bootstrap.rtl.min.mkdi438znt.css", "AssetFile": "bootstrap/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "232026"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"yjpGvf/m1/C8zXmeeS0w+5yNaaUQDRA+gZBv2MmgLRQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mkdi438znt"}, {"Name": "integrity", "Value": "sha256-yjpGvf/m1/C8zXmeeS0w+5yNaaUQDRA+gZBv2MmgLRQ="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.rtl.min.css"}]}, {"Route": "bootstrap/css/bootstrap.rtl.min.mkdi438znt.css.gz", "AssetFile": "bootstrap/css/bootstrap.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30953"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lgdAeS4cP/OuaRmw5emVPRpxlsabNKuIK4GMUqfNsto=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mkdi438znt"}, {"Name": "integrity", "Value": "sha256-lgdAeS4cP/OuaRmw5emVPRpxlsabNKuIK4GMUqfNsto="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.rtl.min.css.gz"}]}, {"Route": "bootstrap/css/bootstrap.v5auijyfck.css", "AssetFile": "bootstrap/css/bootstrap.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029864118"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33484"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"g4stU6R6QAcIzRUtPX1oEwnDJ+mi5dTSGAsTndCpKCY=\""}, {"Name": "ETag", "Value": "W/\"8+y+JMP1TbUveMxl1SU/Lns5RaeKt5L/zToerNbAbfs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5auijyfck"}, {"Name": "integrity", "Value": "sha256-8+y+JMP1TbUveMxl1SU/Lns5RaeKt5L/zToerNbAbfs="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.css"}]}, {"Route": "bootstrap/css/bootstrap.v5auijyfck.css", "AssetFile": "bootstrap/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "292109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8+y+JMP1TbUveMxl1SU/Lns5RaeKt5L/zToerNbAbfs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5auijyfck"}, {"Name": "integrity", "Value": "sha256-8+y+JMP1TbUveMxl1SU/Lns5RaeKt5L/zToerNbAbfs="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.css"}]}, {"Route": "bootstrap/css/bootstrap.v5auijyfck.css.gz", "AssetFile": "bootstrap/css/bootstrap.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33484"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"g4stU6R6QAcIzRUtPX1oEwnDJ+mi5dTSGAsTndCpKCY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5auijyfck"}, {"Name": "integrity", "Value": "sha256-g4stU6R6QAcIzRUtPX1oEwnDJ+mi5dTSGAsTndCpKCY="}, {"Name": "label", "Value": "bootstrap/css/bootstrap.css.gz"}]}, {"Route": "bootstrap/js/bootstrap.9lvk88m3vj.js", "AssetFile": "bootstrap/js/bootstrap.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000033586351"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "29773"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yvB0hJc+t1r9xnvCvtIyXCaCOC6IoklvqpgrwAQHkRk=\""}, {"Name": "ETag", "Value": "W/\"7SQClsMfihMOfUI0LelaCeuH4L8h9HCN1YlJ38uLf10=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9lvk88m3vj"}, {"Name": "integrity", "Value": "sha256-7SQClsMfihMOfUI0LelaCeuH4L8h9HCN1YlJ38uLf10="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.js"}]}, {"Route": "bootstrap/js/bootstrap.9lvk88m3vj.js", "AssetFile": "bootstrap/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "149887"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7SQClsMfihMOfUI0LelaCeuH4L8h9HCN1YlJ38uLf10=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9lvk88m3vj"}, {"Name": "integrity", "Value": "sha256-7SQClsMfihMOfUI0LelaCeuH4L8h9HCN1YlJ38uLf10="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.js"}]}, {"Route": "bootstrap/js/bootstrap.9lvk88m3vj.js.gz", "AssetFile": "bootstrap/js/bootstrap.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "29773"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yvB0hJc+t1r9xnvCvtIyXCaCOC6IoklvqpgrwAQHkRk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9lvk88m3vj"}, {"Name": "integrity", "Value": "sha256-yvB0hJc+t1r9xnvCvtIyXCaCOC6IoklvqpgrwAQHkRk="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.js.gz"}]}, {"Route": "bootstrap/js/bootstrap.bundle.8km969cgf7.js", "AssetFile": "bootstrap/js/bootstrap.bundle.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022400430"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44641"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OeF3lZi97HF6pq9qFaJeu/uufQeuXTgKaLpVWzaulp4=\""}, {"Name": "ETag", "Value": "W/\"eCRUcLTc5Xtl6sn+Vps0WuAqpq6+VcmRZnR5eGKfEIM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8km969cgf7"}, {"Name": "integrity", "Value": "sha256-eCRUcLTc5Xtl6sn+Vps0WuAqpq6+VcmRZnR5eGKfEIM="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.bundle.js"}]}, {"Route": "bootstrap/js/bootstrap.bundle.8km969cgf7.js", "AssetFile": "bootstrap/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "214067"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eCRUcLTc5Xtl6sn+Vps0WuAqpq6+VcmRZnR5eGKfEIM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8km969cgf7"}, {"Name": "integrity", "Value": "sha256-eCRUcLTc5Xtl6sn+Vps0WuAqpq6+VcmRZnR5eGKfEIM="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.bundle.js"}]}, {"Route": "bootstrap/js/bootstrap.bundle.8km969cgf7.js.gz", "AssetFile": "bootstrap/js/bootstrap.bundle.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44641"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OeF3lZi97HF6pq9qFaJeu/uufQeuXTgKaLpVWzaulp4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8km969cgf7"}, {"Name": "integrity", "Value": "sha256-OeF3lZi97HF6pq9qFaJeu/uufQeuXTgKaLpVWzaulp4="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.bundle.js.gz"}]}, {"Route": "bootstrap/js/bootstrap.bundle.js", "AssetFile": "bootstrap/js/bootstrap.bundle.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022400430"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44641"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OeF3lZi97HF6pq9qFaJeu/uufQeuXTgKaLpVWzaulp4=\""}, {"Name": "ETag", "Value": "W/\"eCRUcLTc5Xtl6sn+Vps0WuAqpq6+VcmRZnR5eGKfEIM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eCRUcLTc5Xtl6sn+Vps0WuAqpq6+VcmRZnR5eGKfEIM="}]}, {"Route": "bootstrap/js/bootstrap.bundle.js", "AssetFile": "bootstrap/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "214067"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eCRUcLTc5Xtl6sn+Vps0WuAqpq6+VcmRZnR5eGKfEIM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eCRUcLTc5Xtl6sn+Vps0WuAqpq6+VcmRZnR5eGKfEIM="}]}, {"Route": "bootstrap/js/bootstrap.bundle.js.62jggolsr8.map", "AssetFile": "bootstrap/js/bootstrap.bundle.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011043866"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "90547"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p1VIwx2Ar1QOpSoMSn9hZL175BIvaCUs5m7ssD1p0F8=\""}, {"Name": "ETag", "Value": "W/\"foTLuLIkCxtv092QBVHF5UeMcyTk1dmk/33IIoPkbXA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "62jggolsr8"}, {"Name": "integrity", "Value": "sha256-foTLuLIkCxtv092QBVHF5UeMcyTk1dmk/33IIoPkbXA="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.bundle.js.map"}]}, {"Route": "bootstrap/js/bootstrap.bundle.js.62jggolsr8.map", "AssetFile": "bootstrap/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "427433"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"foTLuLIkCxtv092QBVHF5UeMcyTk1dmk/33IIoPkbXA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "62jggolsr8"}, {"Name": "integrity", "Value": "sha256-foTLuLIkCxtv092QBVHF5UeMcyTk1dmk/33IIoPkbXA="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.bundle.js.map"}]}, {"Route": "bootstrap/js/bootstrap.bundle.js.62jggolsr8.map.gz", "AssetFile": "bootstrap/js/bootstrap.bundle.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "90547"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p1VIwx2Ar1QOpSoMSn9hZL175BIvaCUs5m7ssD1p0F8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "62jggolsr8"}, {"Name": "integrity", "Value": "sha256-p1VIwx2Ar1QOpSoMSn9hZL175BIvaCUs5m7ssD1p0F8="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.bundle.js.map.gz"}]}, {"Route": "bootstrap/js/bootstrap.bundle.js.gz", "AssetFile": "bootstrap/js/bootstrap.bundle.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44641"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OeF3lZi97HF6pq9qFaJeu/uufQeuXTgKaLpVWzaulp4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OeF3lZi97HF6pq9qFaJeu/uufQeuXTgKaLpVWzaulp4="}]}, {"Route": "bootstrap/js/bootstrap.bundle.js.map", "AssetFile": "bootstrap/js/bootstrap.bundle.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011043866"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "90547"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p1VIwx2Ar1QOpSoMSn9hZL175BIvaCUs5m7ssD1p0F8=\""}, {"Name": "ETag", "Value": "W/\"foTLuLIkCxtv092QBVHF5UeMcyTk1dmk/33IIoPkbXA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-foTLuLIkCxtv092QBVHF5UeMcyTk1dmk/33IIoPkbXA="}]}, {"Route": "bootstrap/js/bootstrap.bundle.js.map", "AssetFile": "bootstrap/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "427433"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"foTLuLIkCxtv092QBVHF5UeMcyTk1dmk/33IIoPkbXA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-foTLuLIkCxtv092QBVHF5UeMcyTk1dmk/33IIoPkbXA="}]}, {"Route": "bootstrap/js/bootstrap.bundle.js.map.gz", "AssetFile": "bootstrap/js/bootstrap.bundle.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "90547"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p1VIwx2Ar1QOpSoMSn9hZL175BIvaCUs5m7ssD1p0F8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p1VIwx2Ar1QOpSoMSn9hZL175BIvaCUs5m7ssD1p0F8="}]}, {"Route": "bootstrap/js/bootstrap.bundle.min.9k0n61x5eu.js", "AssetFile": "bootstrap/js/bootstrap.bundle.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041586958"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24045"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cnSaX0vH+ISJfH2Hfu73OOQsLerD3XYhpbJz6PmTfds=\""}, {"Name": "ETag", "Value": "W/\"mRzjzlg4c68pjps4Qa5bSC/KutTlS3Xt83j0SUdjpxU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9k0n61x5eu"}, {"Name": "integrity", "Value": "sha256-mRzjzlg4c68pjps4Qa5bSC/KutTlS3Xt83j0SUdjpxU="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.bundle.min.js"}]}, {"Route": "bootstrap/js/bootstrap.bundle.min.9k0n61x5eu.js", "AssetFile": "bootstrap/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "80727"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mRzjzlg4c68pjps4Qa5bSC/KutTlS3Xt83j0SUdjpxU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9k0n61x5eu"}, {"Name": "integrity", "Value": "sha256-mRzjzlg4c68pjps4Qa5bSC/KutTlS3Xt83j0SUdjpxU="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.bundle.min.js"}]}, {"Route": "bootstrap/js/bootstrap.bundle.min.9k0n61x5eu.js.gz", "AssetFile": "bootstrap/js/bootstrap.bundle.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24045"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cnSaX0vH+ISJfH2Hfu73OOQsLerD3XYhpbJz6PmTfds=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9k0n61x5eu"}, {"Name": "integrity", "Value": "sha256-cnSaX0vH+ISJfH2Hfu73OOQsLerD3XYhpbJz6PmTfds="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.bundle.min.js.gz"}]}, {"Route": "bootstrap/js/bootstrap.bundle.min.js", "AssetFile": "bootstrap/js/bootstrap.bundle.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041586958"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24045"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cnSaX0vH+ISJfH2Hfu73OOQsLerD3XYhpbJz6PmTfds=\""}, {"Name": "ETag", "Value": "W/\"mRzjzlg4c68pjps4Qa5bSC/KutTlS3Xt83j0SUdjpxU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mRzjzlg4c68pjps4Qa5bSC/KutTlS3Xt83j0SUdjpxU="}]}, {"Route": "bootstrap/js/bootstrap.bundle.min.js", "AssetFile": "bootstrap/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "80727"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mRzjzlg4c68pjps4Qa5bSC/KutTlS3Xt83j0SUdjpxU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mRzjzlg4c68pjps4Qa5bSC/KutTlS3Xt83j0SUdjpxU="}]}, {"Route": "bootstrap/js/bootstrap.bundle.min.js.7g010t2zdz.map", "AssetFile": "bootstrap/js/bootstrap.bundle.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011500598"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86951"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FL9IFuQELaJ9bp2L1nCuNTKgIRtUkw8QFyr09nvCBzU=\""}, {"Name": "ETag", "Value": "W/\"edPQ7BeYucniO6//2HhRgG/MDudyk2f6JPuDOItsanQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7g010t2zdz"}, {"Name": "integrity", "Value": "sha256-edPQ7BeYucniO6//2HhRgG/MDudyk2f6JPuDOItsanQ="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.bundle.min.js.map"}]}, {"Route": "bootstrap/js/bootstrap.bundle.min.js.7g010t2zdz.map", "AssetFile": "bootstrap/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "332229"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"edPQ7BeYucniO6//2HhRgG/MDudyk2f6JPuDOItsanQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7g010t2zdz"}, {"Name": "integrity", "Value": "sha256-edPQ7BeYucniO6//2HhRgG/MDudyk2f6JPuDOItsanQ="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.bundle.min.js.map"}]}, {"Route": "bootstrap/js/bootstrap.bundle.min.js.7g010t2zdz.map.gz", "AssetFile": "bootstrap/js/bootstrap.bundle.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86951"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FL9IFuQELaJ9bp2L1nCuNTKgIRtUkw8QFyr09nvCBzU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7g010t2zdz"}, {"Name": "integrity", "Value": "sha256-FL9IFuQELaJ9bp2L1nCuNTKgIRtUkw8QFyr09nvCBzU="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.bundle.min.js.map.gz"}]}, {"Route": "bootstrap/js/bootstrap.bundle.min.js.gz", "AssetFile": "bootstrap/js/bootstrap.bundle.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24045"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cnSaX0vH+ISJfH2Hfu73OOQsLerD3XYhpbJz6PmTfds=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cnSaX0vH+ISJfH2Hfu73OOQsLerD3XYhpbJz6PmTfds="}]}, {"Route": "bootstrap/js/bootstrap.bundle.min.js.map", "AssetFile": "bootstrap/js/bootstrap.bundle.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011500598"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86951"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FL9IFuQELaJ9bp2L1nCuNTKgIRtUkw8QFyr09nvCBzU=\""}, {"Name": "ETag", "Value": "W/\"edPQ7BeYucniO6//2HhRgG/MDudyk2f6JPuDOItsanQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-edPQ7BeYucniO6//2HhRgG/MDudyk2f6JPuDOItsanQ="}]}, {"Route": "bootstrap/js/bootstrap.bundle.min.js.map", "AssetFile": "bootstrap/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "332229"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"edPQ7BeYucniO6//2HhRgG/MDudyk2f6JPuDOItsanQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-edPQ7BeYucniO6//2HhRgG/MDudyk2f6JPuDOItsanQ="}]}, {"Route": "bootstrap/js/bootstrap.bundle.min.js.map.gz", "AssetFile": "bootstrap/js/bootstrap.bundle.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86951"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FL9IFuQELaJ9bp2L1nCuNTKgIRtUkw8QFyr09nvCBzU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FL9IFuQELaJ9bp2L1nCuNTKgIRtUkw8QFyr09nvCBzU="}]}, {"Route": "bootstrap/js/bootstrap.esm.aslu997agw.js", "AssetFile": "bootstrap/js/bootstrap.esm.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000034428148"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "29045"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UmMfS7WwxImNbP+tEuOzMamYqdyqbs9oZsON8kDXkkQ=\""}, {"Name": "ETag", "Value": "W/\"yiYUE/u6aNVpBbHEjGn/9TzWWcpYQLDISFL8y/HnU/c=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aslu997agw"}, {"Name": "integrity", "Value": "sha256-yiYUE/u6aNVpBbHEjGn/9TzWWcpYQLDISFL8y/HnU/c="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.esm.js"}]}, {"Route": "bootstrap/js/bootstrap.esm.aslu997agw.js", "AssetFile": "bootstrap/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "140270"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yiYUE/u6aNVpBbHEjGn/9TzWWcpYQLDISFL8y/HnU/c=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aslu997agw"}, {"Name": "integrity", "Value": "sha256-yiYUE/u6aNVpBbHEjGn/9TzWWcpYQLDISFL8y/HnU/c="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.esm.js"}]}, {"Route": "bootstrap/js/bootstrap.esm.aslu997agw.js.gz", "AssetFile": "bootstrap/js/bootstrap.esm.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "29045"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UmMfS7WwxImNbP+tEuOzMamYqdyqbs9oZsON8kDXkkQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aslu997agw"}, {"Name": "integrity", "Value": "sha256-UmMfS7WwxImNbP+tEuOzMamYqdyqbs9oZsON8kDXkkQ="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.esm.js.gz"}]}, {"Route": "bootstrap/js/bootstrap.esm.js", "AssetFile": "bootstrap/js/bootstrap.esm.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000034428148"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "29045"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UmMfS7WwxImNbP+tEuOzMamYqdyqbs9oZsON8kDXkkQ=\""}, {"Name": "ETag", "Value": "W/\"yiYUE/u6aNVpBbHEjGn/9TzWWcpYQLDISFL8y/HnU/c=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yiYUE/u6aNVpBbHEjGn/9TzWWcpYQLDISFL8y/HnU/c="}]}, {"Route": "bootstrap/js/bootstrap.esm.js", "AssetFile": "bootstrap/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "140270"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yiYUE/u6aNVpBbHEjGn/9TzWWcpYQLDISFL8y/HnU/c=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yiYUE/u6aNVpBbHEjGn/9TzWWcpYQLDISFL8y/HnU/c="}]}, {"Route": "bootstrap/js/bootstrap.esm.js.gz", "AssetFile": "bootstrap/js/bootstrap.esm.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "29045"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UmMfS7WwxImNbP+tEuOzMamYqdyqbs9oZsON8kDXkkQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UmMfS7WwxImNbP+tEuOzMamYqdyqbs9oZsON8kDXkkQ="}]}, {"Route": "bootstrap/js/bootstrap.esm.js.map", "AssetFile": "bootstrap/js/bootstrap.esm.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015865964"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "63027"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oKkH7Vg/AEu9MShcqqf0o8s2L3xmuhDfjigy/sa4uuo=\""}, {"Name": "ETag", "Value": "W/\"G4SjPZxQIAxLXVZg3RifZTDC/aQCoL/tcwyCkDkAILc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G4SjPZxQIAxLXVZg3RifZTDC/aQCoL/tcwyCkDkAILc="}]}, {"Route": "bootstrap/js/bootstrap.esm.js.map", "AssetFile": "bootstrap/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "293318"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"G4SjPZxQIAxLXVZg3RifZTDC/aQCoL/tcwyCkDkAILc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G4SjPZxQIAxLXVZg3RifZTDC/aQCoL/tcwyCkDkAILc="}]}, {"Route": "bootstrap/js/bootstrap.esm.js.map.gz", "AssetFile": "bootstrap/js/bootstrap.esm.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "63027"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oKkH7Vg/AEu9MShcqqf0o8s2L3xmuhDfjigy/sa4uuo=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oKkH7Vg/AEu9MShcqqf0o8s2L3xmuhDfjigy/sa4uuo="}]}, {"Route": "bootstrap/js/bootstrap.esm.js.r8ugzh2us7.map", "AssetFile": "bootstrap/js/bootstrap.esm.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015865964"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "63027"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oKkH7Vg/AEu9MShcqqf0o8s2L3xmuhDfjigy/sa4uuo=\""}, {"Name": "ETag", "Value": "W/\"G4SjPZxQIAxLXVZg3RifZTDC/aQCoL/tcwyCkDkAILc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r8ugzh2us7"}, {"Name": "integrity", "Value": "sha256-G4SjPZxQIAxLXVZg3RifZTDC/aQCoL/tcwyCkDkAILc="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.esm.js.map"}]}, {"Route": "bootstrap/js/bootstrap.esm.js.r8ugzh2us7.map", "AssetFile": "bootstrap/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "293318"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"G4SjPZxQIAxLXVZg3RifZTDC/aQCoL/tcwyCkDkAILc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r8ugzh2us7"}, {"Name": "integrity", "Value": "sha256-G4SjPZxQIAxLXVZg3RifZTDC/aQCoL/tcwyCkDkAILc="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.esm.js.map"}]}, {"Route": "bootstrap/js/bootstrap.esm.js.r8ugzh2us7.map.gz", "AssetFile": "bootstrap/js/bootstrap.esm.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "63027"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oKkH7Vg/AEu9MShcqqf0o8s2L3xmuhDfjigy/sa4uuo=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r8ugzh2us7"}, {"Name": "integrity", "Value": "sha256-oKkH7Vg/AEu9MShcqqf0o8s2L3xmuhDfjigy/sa4uuo="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.esm.js.map.gz"}]}, {"Route": "bootstrap/js/bootstrap.esm.min.e4kyrecbvb.js", "AssetFile": "bootstrap/js/bootstrap.esm.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000053610679"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18652"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xrpG3Sq/TOJ9iDb8tapHQboTGIMJCyHUSOhXRZDYZOA=\""}, {"Name": "ETag", "Value": "W/\"Xg3+bdzAEWYetY+ZTMuiOpqmS2bmwopeaGbXuwqlcpw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e4kyrecbvb"}, {"Name": "integrity", "Value": "sha256-Xg3+bdzAEWYetY+ZTMuiOpqmS2bmwopeaGbXuwqlcpw="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.esm.min.js"}]}, {"Route": "bootstrap/js/bootstrap.esm.min.e4kyrecbvb.js", "AssetFile": "bootstrap/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "73966"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Xg3+bdzAEWYetY+ZTMuiOpqmS2bmwopeaGbXuwqlcpw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e4kyrecbvb"}, {"Name": "integrity", "Value": "sha256-Xg3+bdzAEWYetY+ZTMuiOpqmS2bmwopeaGbXuwqlcpw="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.esm.min.js"}]}, {"Route": "bootstrap/js/bootstrap.esm.min.e4kyrecbvb.js.gz", "AssetFile": "bootstrap/js/bootstrap.esm.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18652"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xrpG3Sq/TOJ9iDb8tapHQboTGIMJCyHUSOhXRZDYZOA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e4kyrecbvb"}, {"Name": "integrity", "Value": "sha256-xrpG3Sq/TOJ9iDb8tapHQboTGIMJCyHUSOhXRZDYZOA="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.esm.min.js.gz"}]}, {"Route": "bootstrap/js/bootstrap.esm.min.js", "AssetFile": "bootstrap/js/bootstrap.esm.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000053610679"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18652"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xrpG3Sq/TOJ9iDb8tapHQboTGIMJCyHUSOhXRZDYZOA=\""}, {"Name": "ETag", "Value": "W/\"Xg3+bdzAEWYetY+ZTMuiOpqmS2bmwopeaGbXuwqlcpw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xg3+bdzAEWYetY+ZTMuiOpqmS2bmwopeaGbXuwqlcpw="}]}, {"Route": "bootstrap/js/bootstrap.esm.min.js", "AssetFile": "bootstrap/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "73966"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Xg3+bdzAEWYetY+ZTMuiOpqmS2bmwopeaGbXuwqlcpw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xg3+bdzAEWYetY+ZTMuiOpqmS2bmwopeaGbXuwqlcpw="}]}, {"Route": "bootstrap/js/bootstrap.esm.min.js.dgkg9c5std.map", "AssetFile": "bootstrap/js/bootstrap.esm.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017662228"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56617"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"yNacvSqKcU4noz924ze5fx8u6SxpCfgWLH/h59hXlBw=\""}, {"Name": "ETag", "Value": "W/\"c4tCfr790lTUcL/G+oNCTjNlEooYbfCUzbw0kzcXguA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dgkg9c5std"}, {"Name": "integrity", "Value": "sha256-c4tCfr790lTUcL/G+oNCTjNlEooYbfCUzbw0kzcXguA="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.esm.min.js.map"}]}, {"Route": "bootstrap/js/bootstrap.esm.min.js.dgkg9c5std.map", "AssetFile": "bootstrap/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "222477"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"c4tCfr790lTUcL/G+oNCTjNlEooYbfCUzbw0kzcXguA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dgkg9c5std"}, {"Name": "integrity", "Value": "sha256-c4tCfr790lTUcL/G+oNCTjNlEooYbfCUzbw0kzcXguA="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.esm.min.js.map"}]}, {"Route": "bootstrap/js/bootstrap.esm.min.js.dgkg9c5std.map.gz", "AssetFile": "bootstrap/js/bootstrap.esm.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56617"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"yNacvSqKcU4noz924ze5fx8u6SxpCfgWLH/h59hXlBw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dgkg9c5std"}, {"Name": "integrity", "Value": "sha256-yNacvSqKcU4noz924ze5fx8u6SxpCfgWLH/h59hXlBw="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.esm.min.js.map.gz"}]}, {"Route": "bootstrap/js/bootstrap.esm.min.js.gz", "AssetFile": "bootstrap/js/bootstrap.esm.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18652"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xrpG3Sq/TOJ9iDb8tapHQboTGIMJCyHUSOhXRZDYZOA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xrpG3Sq/TOJ9iDb8tapHQboTGIMJCyHUSOhXRZDYZOA="}]}, {"Route": "bootstrap/js/bootstrap.esm.min.js.map", "AssetFile": "bootstrap/js/bootstrap.esm.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017662228"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56617"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"yNacvSqKcU4noz924ze5fx8u6SxpCfgWLH/h59hXlBw=\""}, {"Name": "ETag", "Value": "W/\"c4tCfr790lTUcL/G+oNCTjNlEooYbfCUzbw0kzcXguA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c4tCfr790lTUcL/G+oNCTjNlEooYbfCUzbw0kzcXguA="}]}, {"Route": "bootstrap/js/bootstrap.esm.min.js.map", "AssetFile": "bootstrap/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "222477"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"c4tCfr790lTUcL/G+oNCTjNlEooYbfCUzbw0kzcXguA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c4tCfr790lTUcL/G+oNCTjNlEooYbfCUzbw0kzcXguA="}]}, {"Route": "bootstrap/js/bootstrap.esm.min.js.map.gz", "AssetFile": "bootstrap/js/bootstrap.esm.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56617"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"yNacvSqKcU4noz924ze5fx8u6SxpCfgWLH/h59hXlBw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yNacvSqKcU4noz924ze5fx8u6SxpCfgWLH/h59hXlBw="}]}, {"Route": "bootstrap/js/bootstrap.js", "AssetFile": "bootstrap/js/bootstrap.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000033586351"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "29773"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yvB0hJc+t1r9xnvCvtIyXCaCOC6IoklvqpgrwAQHkRk=\""}, {"Name": "ETag", "Value": "W/\"7SQClsMfihMOfUI0LelaCeuH4L8h9HCN1YlJ38uLf10=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7SQClsMfihMOfUI0LelaCeuH4L8h9HCN1YlJ38uLf10="}]}, {"Route": "bootstrap/js/bootstrap.js", "AssetFile": "bootstrap/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "149887"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7SQClsMfihMOfUI0LelaCeuH4L8h9HCN1YlJ38uLf10=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7SQClsMfihMOfUI0LelaCeuH4L8h9HCN1YlJ38uLf10="}]}, {"Route": "bootstrap/js/bootstrap.js.gz", "AssetFile": "bootstrap/js/bootstrap.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "29773"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yvB0hJc+t1r9xnvCvtIyXCaCOC6IoklvqpgrwAQHkRk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yvB0hJc+t1r9xnvCvtIyXCaCOC6IoklvqpgrwAQHkRk="}]}, {"Route": "bootstrap/js/bootstrap.js.map", "AssetFile": "bootstrap/js/bootstrap.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015791302"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "63325"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sl9Rpp3aLH+E4g4ufzPYjyR0z9GkwFbg5ZYwUbUcckk=\""}, {"Name": "ETag", "Value": "W/\"20K8Xd1AY5wk/a0ThoI9YgG/d64fs+Zf2Hy1b7Yb0S0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-20K8Xd1AY5wk/a0ThoI9YgG/d64fs+Zf2Hy1b7Yb0S0="}]}, {"Route": "bootstrap/js/bootstrap.js.map", "AssetFile": "bootstrap/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "294480"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"20K8Xd1AY5wk/a0ThoI9YgG/d64fs+Zf2Hy1b7Yb0S0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-20K8Xd1AY5wk/a0ThoI9YgG/d64fs+Zf2Hy1b7Yb0S0="}]}, {"Route": "bootstrap/js/bootstrap.js.map.gz", "AssetFile": "bootstrap/js/bootstrap.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "63325"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sl9Rpp3aLH+E4g4ufzPYjyR0z9GkwFbg5ZYwUbUcckk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sl9Rpp3aLH+E4g4ufzPYjyR0z9GkwFbg5ZYwUbUcckk="}]}, {"Route": "bootstrap/js/bootstrap.js.nd7yxyuic7.map", "AssetFile": "bootstrap/js/bootstrap.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015791302"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "63325"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sl9Rpp3aLH+E4g4ufzPYjyR0z9GkwFbg5ZYwUbUcckk=\""}, {"Name": "ETag", "Value": "W/\"20K8Xd1AY5wk/a0ThoI9YgG/d64fs+Zf2Hy1b7Yb0S0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nd7<PERSON><PERSON><PERSON>7"}, {"Name": "integrity", "Value": "sha256-20K8Xd1AY5wk/a0ThoI9YgG/d64fs+Zf2Hy1b7Yb0S0="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.js.map"}]}, {"Route": "bootstrap/js/bootstrap.js.nd7yxyuic7.map", "AssetFile": "bootstrap/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "294480"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"20K8Xd1AY5wk/a0ThoI9YgG/d64fs+Zf2Hy1b7Yb0S0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nd7<PERSON><PERSON><PERSON>7"}, {"Name": "integrity", "Value": "sha256-20K8Xd1AY5wk/a0ThoI9YgG/d64fs+Zf2Hy1b7Yb0S0="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.js.map"}]}, {"Route": "bootstrap/js/bootstrap.js.nd7yxyuic7.map.gz", "AssetFile": "bootstrap/js/bootstrap.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "63325"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sl9Rpp3aLH+E4g4ufzPYjyR0z9GkwFbg5ZYwUbUcckk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nd7<PERSON><PERSON><PERSON>7"}, {"Name": "integrity", "Value": "sha256-sl9Rpp3aLH+E4g4ufzPYjyR0z9GkwFbg5ZYwUbUcckk="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.js.map.gz"}]}, {"Route": "bootstrap/js/bootstrap.min.cpkb327sqr.js", "AssetFile": "bootstrap/js/bootstrap.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000059908938"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16691"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oy24YGQ+Y5gaTLxH8+8ghHO0azGhbw01cgUB9zQvpSc=\""}, {"Name": "ETag", "Value": "W/\"kNioUxMuvZBQIA1hKBRfhm26Eg0kL29Lv+zYMPCo9dU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cpkb327sqr"}, {"Name": "integrity", "Value": "sha256-kNioUxMuvZBQIA1hKBRfhm26Eg0kL29Lv+zYMPCo9dU="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.min.js"}]}, {"Route": "bootstrap/js/bootstrap.min.cpkb327sqr.js", "AssetFile": "bootstrap/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "60681"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kNioUxMuvZBQIA1hKBRfhm26Eg0kL29Lv+zYMPCo9dU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cpkb327sqr"}, {"Name": "integrity", "Value": "sha256-kNioUxMuvZBQIA1hKBRfhm26Eg0kL29Lv+zYMPCo9dU="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.min.js"}]}, {"Route": "bootstrap/js/bootstrap.min.cpkb327sqr.js.gz", "AssetFile": "bootstrap/js/bootstrap.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16691"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oy24YGQ+Y5gaTLxH8+8ghHO0azGhbw01cgUB9zQvpSc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cpkb327sqr"}, {"Name": "integrity", "Value": "sha256-oy24YGQ+Y5gaTLxH8+8ghHO0azGhbw01cgUB9zQvpSc="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.min.js.gz"}]}, {"Route": "bootstrap/js/bootstrap.min.js", "AssetFile": "bootstrap/js/bootstrap.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000059908938"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16691"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oy24YGQ+Y5gaTLxH8+8ghHO0azGhbw01cgUB9zQvpSc=\""}, {"Name": "ETag", "Value": "W/\"kNioUxMuvZBQIA1hKBRfhm26Eg0kL29Lv+zYMPCo9dU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kNioUxMuvZBQIA1hKBRfhm26Eg0kL29Lv+zYMPCo9dU="}]}, {"Route": "bootstrap/js/bootstrap.min.js", "AssetFile": "bootstrap/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "60681"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kNioUxMuvZBQIA1hKBRfhm26Eg0kL29Lv+zYMPCo9dU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kNioUxMuvZBQIA1hKBRfhm26Eg0kL29Lv+zYMPCo9dU="}]}, {"Route": "bootstrap/js/bootstrap.min.js.bi4v2s709r.map", "AssetFile": "bootstrap/js/bootstrap.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017906065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55846"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3+aMagGFsXYWhknpE553wMtct79B/WPvYmAGSvvOvEQ=\""}, {"Name": "ETag", "Value": "W/\"k8oB4N73rmYSSB03631lBDI/yAw5O4jj4kxn2njBM+8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bi4v2s709r"}, {"Name": "integrity", "Value": "sha256-k8oB4N73rmYSSB03631lBDI/yAw5O4jj4kxn2njBM+8="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.min.js.map"}]}, {"Route": "bootstrap/js/bootstrap.min.js.bi4v2s709r.map", "AssetFile": "bootstrap/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "220732"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"k8oB4N73rmYSSB03631lBDI/yAw5O4jj4kxn2njBM+8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bi4v2s709r"}, {"Name": "integrity", "Value": "sha256-k8oB4N73rmYSSB03631lBDI/yAw5O4jj4kxn2njBM+8="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.min.js.map"}]}, {"Route": "bootstrap/js/bootstrap.min.js.bi4v2s709r.map.gz", "AssetFile": "bootstrap/js/bootstrap.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55846"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3+aMagGFsXYWhknpE553wMtct79B/WPvYmAGSvvOvEQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bi4v2s709r"}, {"Name": "integrity", "Value": "sha256-3+aMagGFsXYWhknpE553wMtct79B/WPvYmAGSvvOvEQ="}, {"Name": "label", "Value": "bootstrap/js/bootstrap.min.js.map.gz"}]}, {"Route": "bootstrap/js/bootstrap.min.js.gz", "AssetFile": "bootstrap/js/bootstrap.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16691"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oy24YGQ+Y5gaTLxH8+8ghHO0azGhbw01cgUB9zQvpSc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oy24YGQ+Y5gaTLxH8+8ghHO0azGhbw01cgUB9zQvpSc="}]}, {"Route": "bootstrap/js/bootstrap.min.js.map", "AssetFile": "bootstrap/js/bootstrap.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017906065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55846"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3+aMagGFsXYWhknpE553wMtct79B/WPvYmAGSvvOvEQ=\""}, {"Name": "ETag", "Value": "W/\"k8oB4N73rmYSSB03631lBDI/yAw5O4jj4kxn2njBM+8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k8oB4N73rmYSSB03631lBDI/yAw5O4jj4kxn2njBM+8="}]}, {"Route": "bootstrap/js/bootstrap.min.js.map", "AssetFile": "bootstrap/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "220732"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"k8oB4N73rmYSSB03631lBDI/yAw5O4jj4kxn2njBM+8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k8oB4N73rmYSSB03631lBDI/yAw5O4jj4kxn2njBM+8="}]}, {"Route": "bootstrap/js/bootstrap.min.js.map.gz", "AssetFile": "bootstrap/js/bootstrap.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55846"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3+aMagGFsXYWhknpE553wMtct79B/WPvYmAGSvvOvEQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3+aMagGFsXYWhknpE553wMtct79B/WPvYmAGSvvOvEQ="}]}, {"Route": "css/site.css", "AssetFile": "css/site.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000594530321"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1681"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QtrjBwB2SqKRezsfiY04cxbhKN3mar+BFQSrLy6eoPU=\""}, {"Name": "ETag", "Value": "W/\"b000VlrfBP5KmC/khttvw0iB10G7+yXJd73Ja9YkZ+8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b000VlrfBP5KmC/khttvw0iB10G7+yXJd73Ja9YkZ+8="}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6926"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"b000VlrfBP5KmC/khttvw0iB10G7+yXJd73Ja9YkZ+8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b000VlrfBP5KmC/khttvw0iB10G7+yXJd73Ja9YkZ+8="}]}, {"Route": "css/site.css.gz", "AssetFile": "css/site.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1681"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QtrjBwB2SqKRezsfiY04cxbhKN3mar+BFQSrLy6eoPU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QtrjBwB2SqKRezsfiY04cxbhKN3mar+BFQSrLy6eoPU="}]}, {"Route": "css/site.zzits3csm6.css", "AssetFile": "css/site.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000594530321"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1681"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QtrjBwB2SqKRezsfiY04cxbhKN3mar+BFQSrLy6eoPU=\""}, {"Name": "ETag", "Value": "W/\"b000VlrfBP5KmC/khttvw0iB10G7+yXJd73Ja9YkZ+8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zzits3csm6"}, {"Name": "integrity", "Value": "sha256-b000VlrfBP5KmC/khttvw0iB10G7+yXJd73Ja9YkZ+8="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.zzits3csm6.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6926"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"b000VlrfBP5KmC/khttvw0iB10G7+yXJd73Ja9YkZ+8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zzits3csm6"}, {"Name": "integrity", "Value": "sha256-b000VlrfBP5KmC/khttvw0iB10G7+yXJd73Ja9YkZ+8="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.zzits3csm6.css.gz", "AssetFile": "css/site.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1681"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QtrjBwB2SqKRezsfiY04cxbhKN3mar+BFQSrLy6eoPU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zzits3csm6"}, {"Name": "integrity", "Value": "sha256-QtrjBwB2SqKRezsfiY04cxbhKN3mar+BFQSrLy6eoPU="}, {"Name": "label", "Value": "css/site.css.gz"}]}, {"Route": "images/1.aj5oirlc8f.webp", "AssetFile": "images/1.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "112194"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"+t/WgvZmCBfWjxB3FCbv9y66O8hKJ4ORv0itXgCSYF0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aj5oirlc8f"}, {"Name": "integrity", "Value": "sha256-+t/WgvZmCBfWjxB3FCbv9y66O8hKJ4ORv0itXgCSYF0="}, {"Name": "label", "Value": "images/1.webp"}]}, {"Route": "images/1.webp", "AssetFile": "images/1.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "112194"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"+t/WgvZmCBfWjxB3FCbv9y66O8hKJ4ORv0itXgCSYF0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+t/WgvZmCBfWjxB3FCbv9y66O8hKJ4ORv0itXgCSYF0="}]}, {"Route": "images/2.jpg", "AssetFile": "images/2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "1970174"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"KTR73fVYk2YelXnwaQItdVIZIFVnQj8WB6XyzekZPs4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KTR73fVYk2YelXnwaQItdVIZIFVnQj8WB6XyzekZPs4="}]}, {"Route": "images/2.tjy1fsfi1b.jpg", "AssetFile": "images/2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1970174"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"KTR73fVYk2YelXnwaQItdVIZIFVnQj8WB6XyzekZPs4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tjy1fsfi1b"}, {"Name": "integrity", "Value": "sha256-KTR73fVYk2YelXnwaQItdVIZIFVnQj8WB6XyzekZPs4="}, {"Name": "label", "Value": "images/2.jpg"}]}, {"Route": "images/3.avif", "AssetFile": "images/3.avif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "62224"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0NEpQmE/xh4WFFI4F0Y/MWiYG22m14zV3+ppXm2xhiQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0NEpQmE/xh4WFFI4F0Y/MWiYG22m14zV3+ppXm2xhiQ="}]}, {"Route": "images/3.g4zkf1p9b3.avif", "AssetFile": "images/3.avif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "62224"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0NEpQmE/xh4WFFI4F0Y/MWiYG22m14zV3+ppXm2xhiQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g4zkf1p9b3"}, {"Name": "integrity", "Value": "sha256-0NEpQmE/xh4WFFI4F0Y/MWiYG22m14zV3+ppXm2xhiQ="}, {"Name": "label", "Value": "images/3.avif"}]}, {"Route": "images/4.jpg", "AssetFile": "images/4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "799497"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Qha5HAuem4ezOQ3GhCnE+jKTVUfwp52vlxZtME/Og4o=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Qha5HAuem4ezOQ3GhCnE+jKTVUfwp52vlxZtME/Og4o="}]}, {"Route": "images/4.yrlqdzq3cd.jpg", "AssetFile": "images/4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "799497"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Qha5HAuem4ezOQ3GhCnE+jKTVUfwp52vlxZtME/Og4o=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yrlqdzq3cd"}, {"Name": "integrity", "Value": "sha256-Qha5HAuem4ezOQ3GhCnE+jKTVUfwp52vlxZtME/Og4o="}, {"Name": "label", "Value": "images/4.jpg"}]}, {"Route": "images/5.jpg", "AssetFile": "images/5.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "137305"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"P61yu2r6RqPSLP3Mgs8GkQDgSebNvCabQdjfGnv0yT8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P61yu2r6RqPSLP3Mgs8GkQDgSebNvCabQdjfGnv0yT8="}]}, {"Route": "images/5.x0m5sqd1ty.jpg", "AssetFile": "images/5.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "137305"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"P61yu2r6RqPSLP3Mgs8GkQDgSebNvCabQdjfGnv0yT8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0m5sqd1ty"}, {"Name": "integrity", "Value": "sha256-P61yu2r6RqPSLP3Mgs8GkQDgSebNvCabQdjfGnv0yT8="}, {"Name": "label", "Value": "images/5.jpg"}]}, {"Route": "images/biogas.svg", "AssetFile": "images/biogas.svg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002624671916"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "380"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"fuZLHb0zu5eGODhdsc5Pkz/Xz2My34MdkbROqgpgQkI=\""}, {"Name": "ETag", "Value": "W/\"jj/1DJa+Wc7iWBzH5NqqPhJqW/zR43daRKaWjcI+TR4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jj/1DJa+Wc7iWBzH5NqqPhJqW/zR43daRKaWjcI+TR4="}]}, {"Route": "images/biogas.svg", "AssetFile": "images/biogas.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "744"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"jj/1DJa+Wc7iWBzH5NqqPhJqW/zR43daRKaWjcI+TR4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jj/1DJa+Wc7iWBzH5NqqPhJqW/zR43daRKaWjcI+TR4="}]}, {"Route": "images/biogas.svg.gz", "AssetFile": "images/biogas.svg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "380"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"fuZLHb0zu5eGODhdsc5Pkz/Xz2My34MdkbROqgpgQkI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fuZLHb0zu5eGODhdsc5Pkz/Xz2My34MdkbROqgpgQkI="}]}, {"Route": "images/biogas.ycmpjwhwht.svg", "AssetFile": "images/biogas.svg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002624671916"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "380"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"fuZLHb0zu5eGODhdsc5Pkz/Xz2My34MdkbROqgpgQkI=\""}, {"Name": "ETag", "Value": "W/\"jj/1DJa+Wc7iWBzH5NqqPhJqW/zR43daRKaWjcI+TR4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ycmpjwhwht"}, {"Name": "integrity", "Value": "sha256-jj/1DJa+Wc7iWBzH5NqqPhJqW/zR43daRKaWjcI+TR4="}, {"Name": "label", "Value": "images/biogas.svg"}]}, {"Route": "images/biogas.ycmpjwhwht.svg", "AssetFile": "images/biogas.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "744"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"jj/1DJa+Wc7iWBzH5NqqPhJqW/zR43daRKaWjcI+TR4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ycmpjwhwht"}, {"Name": "integrity", "Value": "sha256-jj/1DJa+Wc7iWBzH5NqqPhJqW/zR43daRKaWjcI+TR4="}, {"Name": "label", "Value": "images/biogas.svg"}]}, {"Route": "images/biogas.ycmpjwhwht.svg.gz", "AssetFile": "images/biogas.svg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "380"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"fuZLHb0zu5eGODhdsc5Pkz/Xz2My34MdkbROqgpgQkI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:08 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ycmpjwhwht"}, {"Name": "integrity", "Value": "sha256-fuZLHb0zu5eGODhdsc5Pkz/Xz2My34MdkbROqgpgQkI="}, {"Name": "label", "Value": "images/biogas.svg.gz"}]}, {"Route": "images/energy-placeholder.svg", "AssetFile": "images/energy-placeholder.svg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000459347726"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2176"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"BPFxV6m1BjKHmIYY8HxFBvSci0ITjfXucBDNnKKMko8=\""}, {"Name": "ETag", "Value": "W/\"1HwrtwOLFxiy4P0+LxNN59wZdAxkCS/R3LS7SGZwbd8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 20:42:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1HwrtwOLFxiy4P0+LxNN59wZdAxkCS/R3LS7SGZwbd8="}]}, {"Route": "images/energy-placeholder.svg", "AssetFile": "images/energy-placeholder.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "5403"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"1HwrtwOLFxiy4P0+LxNN59wZdAxkCS/R3LS7SGZwbd8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 20:42:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1HwrtwOLFxiy4P0+LxNN59wZdAxkCS/R3LS7SGZwbd8="}]}, {"Route": "images/energy-placeholder.svg.gz", "AssetFile": "images/energy-placeholder.svg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2176"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"BPFxV6m1BjKHmIYY8HxFBvSci0ITjfXucBDNnKKMko8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 20:42:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BPFxV6m1BjKHmIYY8HxFBvSci0ITjfXucBDNnKKMko8="}]}, {"Route": "images/energy-placeholder.w36pkxegsk.svg", "AssetFile": "images/energy-placeholder.svg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000459347726"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2176"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"BPFxV6m1BjKHmIYY8HxFBvSci0ITjfXucBDNnKKMko8=\""}, {"Name": "ETag", "Value": "W/\"1HwrtwOLFxiy4P0+LxNN59wZdAxkCS/R3LS7SGZwbd8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 20:42:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w36pkxegsk"}, {"Name": "integrity", "Value": "sha256-1HwrtwOLFxiy4P0+LxNN59wZdAxkCS/R3LS7SGZwbd8="}, {"Name": "label", "Value": "images/energy-placeholder.svg"}]}, {"Route": "images/energy-placeholder.w36pkxegsk.svg", "AssetFile": "images/energy-placeholder.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5403"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"1HwrtwOLFxiy4P0+LxNN59wZdAxkCS/R3LS7SGZwbd8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 20:42:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w36pkxegsk"}, {"Name": "integrity", "Value": "sha256-1HwrtwOLFxiy4P0+LxNN59wZdAxkCS/R3LS7SGZwbd8="}, {"Name": "label", "Value": "images/energy-placeholder.svg"}]}, {"Route": "images/energy-placeholder.w36pkxegsk.svg.gz", "AssetFile": "images/energy-placeholder.svg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2176"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"BPFxV6m1BjKHmIYY8HxFBvSci0ITjfXucBDNnKKMko8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 20:42:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w36pkxegsk"}, {"Name": "integrity", "Value": "sha256-BPFxV6m1BjKHmIYY8HxFBvSci0ITjfXucBDNnKKMko8="}, {"Name": "label", "Value": "images/energy-placeholder.svg.gz"}]}, {"Route": "images/logo.svg", "AssetFile": "images/logo.svg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002840909091"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "351"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"EjC1bMCRZwcHKi8LQooTZaX3laswUPuLPnO2o6SKK8U=\""}, {"Name": "ETag", "Value": "W/\"KVLtj/4OlN5TMJ2JYeTuOSc2GvUGAFnAY9Q5eyM2vFg=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KVLtj/4OlN5TMJ2JYeTuOSc2GvUGAFnAY9Q5eyM2vFg="}]}, {"Route": "images/logo.svg", "AssetFile": "images/logo.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "562"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"KVLtj/4OlN5TMJ2JYeTuOSc2GvUGAFnAY9Q5eyM2vFg=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KVLtj/4OlN5TMJ2JYeTuOSc2GvUGAFnAY9Q5eyM2vFg="}]}, {"Route": "images/logo.svg.gz", "AssetFile": "images/logo.svg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "351"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"EjC1bMCRZwcHKi8LQooTZaX3laswUPuLPnO2o6SKK8U=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EjC1bMCRZwcHKi8LQooTZaX3laswUPuLPnO2o6SKK8U="}]}, {"Route": "images/logo.xygpc539h1.svg", "AssetFile": "images/logo.svg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002840909091"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "351"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"EjC1bMCRZwcHKi8LQooTZaX3laswUPuLPnO2o6SKK8U=\""}, {"Name": "ETag", "Value": "W/\"KVLtj/4OlN5TMJ2JYeTuOSc2GvUGAFnAY9Q5eyM2vFg=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xygpc539h1"}, {"Name": "integrity", "Value": "sha256-KVLtj/4OlN5TMJ2JYeTuOSc2GvUGAFnAY9Q5eyM2vFg="}, {"Name": "label", "Value": "images/logo.svg"}]}, {"Route": "images/logo.xygpc539h1.svg", "AssetFile": "images/logo.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "562"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"KVLtj/4OlN5TMJ2JYeTuOSc2GvUGAFnAY9Q5eyM2vFg=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xygpc539h1"}, {"Name": "integrity", "Value": "sha256-KVLtj/4OlN5TMJ2JYeTuOSc2GvUGAFnAY9Q5eyM2vFg="}, {"Name": "label", "Value": "images/logo.svg"}]}, {"Route": "images/logo.xygpc539h1.svg.gz", "AssetFile": "images/logo.svg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "351"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"EjC1bMCRZwcHKi8LQooTZaX3laswUPuLPnO2o6SKK8U=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xygpc539h1"}, {"Name": "integrity", "Value": "sha256-EjC1bMCRZwcHKi8LQooTZaX3laswUPuLPnO2o6SKK8U="}, {"Name": "label", "Value": "images/logo.svg.gz"}]}, {"Route": "images/solar-panel.gtianxm0qd.svg", "AssetFile": "images/solar-panel.svg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002898550725"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "344"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"YGbrp7iSJP3XISEu3nR0pDxRnNabh1P0Z8YVmhQMLFw=\""}, {"Name": "ETag", "Value": "W/\"PIfiepxJiBnYhcyAzSOyuwxAIg+c64ud0pDKhCXTmII=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gtianxm0qd"}, {"Name": "integrity", "Value": "sha256-PIfiepxJiBnYhcyAzSOyuwxAIg+c64ud0pDKhCXTmII="}, {"Name": "label", "Value": "images/solar-panel.svg"}]}, {"Route": "images/solar-panel.gtianxm0qd.svg", "AssetFile": "images/solar-panel.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "891"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"PIfiepxJiBnYhcyAzSOyuwxAIg+c64ud0pDKhCXTmII=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gtianxm0qd"}, {"Name": "integrity", "Value": "sha256-PIfiepxJiBnYhcyAzSOyuwxAIg+c64ud0pDKhCXTmII="}, {"Name": "label", "Value": "images/solar-panel.svg"}]}, {"Route": "images/solar-panel.gtianxm0qd.svg.gz", "AssetFile": "images/solar-panel.svg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "344"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"YGbrp7iSJP3XISEu3nR0pDxRnNabh1P0Z8YVmhQMLFw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gtianxm0qd"}, {"Name": "integrity", "Value": "sha256-YGbrp7iSJP3XISEu3nR0pDxRnNabh1P0Z8YVmhQMLFw="}, {"Name": "label", "Value": "images/solar-panel.svg.gz"}]}, {"Route": "images/solar-panel.svg", "AssetFile": "images/solar-panel.svg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002898550725"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "344"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"YGbrp7iSJP3XISEu3nR0pDxRnNabh1P0Z8YVmhQMLFw=\""}, {"Name": "ETag", "Value": "W/\"PIfiepxJiBnYhcyAzSOyuwxAIg+c64ud0pDKhCXTmII=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PIfiepxJiBnYhcyAzSOyuwxAIg+c64ud0pDKhCXTmII="}]}, {"Route": "images/solar-panel.svg", "AssetFile": "images/solar-panel.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "891"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"PIfiepxJiBnYhcyAzSOyuwxAIg+c64ud0pDKhCXTmII=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PIfiepxJiBnYhcyAzSOyuwxAIg+c64ud0pDKhCXTmII="}]}, {"Route": "images/solar-panel.svg.gz", "AssetFile": "images/solar-panel.svg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "344"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"YGbrp7iSJP3XISEu3nR0pDxRnNabh1P0Z8YVmhQMLFw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YGbrp7iSJP3XISEu3nR0pDxRnNabh1P0Z8YVmhQMLFw="}]}, {"Route": "images/user-placeholder.b5hoom9qfk.png", "AssetFile": "images/user-placeholder.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "159545"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"z6zLZ9sXKpRbSVeJ/3MWEbHqAzwwihJI9Ef16CvKxfk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b5hoom9qfk"}, {"Name": "integrity", "Value": "sha256-z6zLZ9sXKpRbSVeJ/3MWEbHqAzwwihJI9Ef16CvKxfk="}, {"Name": "label", "Value": "images/user-placeholder.png"}]}, {"Route": "images/user-placeholder.png", "AssetFile": "images/user-placeholder.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "159545"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"z6zLZ9sXKpRbSVeJ/3MWEbHqAzwwihJI9Ef16CvKxfk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z6zLZ9sXKpRbSVeJ/3MWEbHqAzwwihJI9Ef16CvKxfk="}]}, {"Route": "images/wind-turbine.svg", "AssetFile": "images/wind-turbine.svg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002680965147"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "372"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"2QIpa0yp0K6vNicUd/Kjl4KA1k45B2qLgI0hnGLz91k=\""}, {"Name": "ETag", "Value": "W/\"yymFhVnDxwTApBgtRTKa8lvo/ekYno5sUIzX83vL9H4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yymFhVnDxwTApBgtRTKa8lvo/ekYno5sUIzX83vL9H4="}]}, {"Route": "images/wind-turbine.svg", "AssetFile": "images/wind-turbine.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "795"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"yymFhVnDxwTApBgtRTKa8lvo/ekYno5sUIzX83vL9H4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yymFhVnDxwTApBgtRTKa8lvo/ekYno5sUIzX83vL9H4="}]}, {"Route": "images/wind-turbine.svg.gz", "AssetFile": "images/wind-turbine.svg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "372"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"2QIpa0yp0K6vNicUd/Kjl4KA1k45B2qLgI0hnGLz91k=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2QIpa0yp0K6vNicUd/Kjl4KA1k45B2qLgI0hnGLz91k="}]}, {"Route": "images/wind-turbine.x1nuq7zg5q.svg", "AssetFile": "images/wind-turbine.svg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002680965147"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "372"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"2QIpa0yp0K6vNicUd/Kjl4KA1k45B2qLgI0hnGLz91k=\""}, {"Name": "ETag", "Value": "W/\"yymFhVnDxwTApBgtRTKa8lvo/ekYno5sUIzX83vL9H4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x1nuq7zg5q"}, {"Name": "integrity", "Value": "sha256-yymFhVnDxwTApBgtRTKa8lvo/ekYno5sUIzX83vL9H4="}, {"Name": "label", "Value": "images/wind-turbine.svg"}]}, {"Route": "images/wind-turbine.x1nuq7zg5q.svg", "AssetFile": "images/wind-turbine.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "795"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"yymFhVnDxwTApBgtRTKa8lvo/ekYno5sUIzX83vL9H4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x1nuq7zg5q"}, {"Name": "integrity", "Value": "sha256-yymFhVnDxwTApBgtRTKa8lvo/ekYno5sUIzX83vL9H4="}, {"Name": "label", "Value": "images/wind-turbine.svg"}]}, {"Route": "images/wind-turbine.x1nuq7zg5q.svg.gz", "AssetFile": "images/wind-turbine.svg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "372"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"2QIpa0yp0K6vNicUd/Kjl4KA1k45B2qLgI0hnGLz91k=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 18:06:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x1nuq7zg5q"}, {"Name": "integrity", "Value": "sha256-2QIpa0yp0K6vNicUd/Kjl4KA1k45B2qLgI0hnGLz91k="}, {"Name": "label", "Value": "images/wind-turbine.svg.gz"}]}]}