public class HomePageData
{
    public Statistics Stats { get; set; } = new();
    public List<Testimonial> Testimonials { get; set; } = new();
    public List<EnergySolution> Solutions { get; set; } = new();
}

public class Statistics
{
    public int Id { get; set; }
    public int FarmersConnected { get; set; }
    public int EnergyExperts { get; set; }
    public int ProjectsCompleted { get; set; }
    public int CostSavingsPercentage { get; set; }
}

public class Testimonial
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public string ImageUrl { get; set; } = string.Empty;
    public string Comment { get; set; } = string.Empty;
    public int Rating { get; set; }
}

public class EnergySolution
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
}