using Microsoft.EntityFrameworkCore;
using AgriEnergyPlatform.Data;
using AgriEnergyPlatform.Models;

namespace AgriEnergyPlatform.Services
{
    public class ExpertService : IExpertService
    {
        private readonly ApplicationDbContext _context;

        public ExpertService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<Expert>> GetExperts()
        {
            return await _context.Experts.ToListAsync();
        }

        public async Task<Expert?> GetExpertById(int id)
        {
            return await _context.Experts.FindAsync(id);
        }
    }
}