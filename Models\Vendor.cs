using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity; // Required for ApplicationUser if not already present, but ApplicationUser is in the same namespace so it might not be strictly needed here.

namespace AgriEnergyPlatform.Models
{
    public class Vendor
    {
        [Key]
        public int Id { get; set; }
        public string UserId { get; set; } = string.Empty; // Changed to string to link with ApplicationUser
        public ApplicationUser? ApplicationUser { get; set; } // Navigation property
        [Required]
        public string CompanyName { get; set; } = string.Empty;
        [Required]
        public string BusinessType { get; set; } = string.Empty;
        public string LicenseNumber { get; set; } = string.Empty;
        public string TaxId { get; set; } = string.Empty;
        public bool IsVerified { get; set; }
        public DateTime RegisteredDate { get; set; } = DateTime.UtcNow;
        public string Website { get; set; } = string.Empty;
        public string ContactEmail { get; set; } = string.Empty;
        public string ContactPhone { get; set; } = string.Empty;
        public double Rating { get; set; }
    }
}