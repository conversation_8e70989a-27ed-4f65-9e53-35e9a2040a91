// Add this using directive at the top
using Microsoft.AspNetCore.Identity;
using AgriEnergyPlatform.Models;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace AgriEnergyPlatform.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<EducationalContent> EducationalContents { get; set; }
        public DbSet<ExpertTraining> ExpertTrainings { get; set; }
        public DbSet<TrainingEvent> TrainingEvents { get; set; }  // Add this line
        public DbSet<EnergyProduct> EnergyProducts { get; set; }
        public DbSet<EnergySolution> EnergySolutions { get; set; }
        public DbSet<Expert> Experts { get; set; }
        public DbSet<EventRegistration> EventRegistrations { get; set; }
        public DbSet<Vendor> Vendors { get; set; }
        public DbSet<Testimonial> Testimonials { get; set; }
        public DbSet<Statistics> Statistics { get; set; }
        public DbSet<ForumTopic> ForumTopics { get; set; }
        public DbSet<Topic> Topics { get; set; }
        public DbSet<Comment> Comments { get; set; }
        public DbSet<TopicLike> TopicLikes { get; set; }
        public DbSet<TrainingEnrollment> TrainingEnrollments { get; set; }
        public DbSet<SolutionFeedback> SolutionFeedbacks { get; set; }
        public DbSet<ExpertMessage> ExpertMessages { get; set; }
        public DbSet<Venue> Venues { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Topic
            modelBuilder.Entity<Topic>()
                .HasKey(t => t.Id);
            modelBuilder.Entity<Topic>()
                .HasOne(t => t.User)
                .WithMany()
                .HasForeignKey(t => t.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure Comment
            modelBuilder.Entity<Comment>()
                .HasKey(c => c.Id);
            modelBuilder.Entity<Comment>()
                .HasOne(c => c.ForumTopic)
                .WithMany(t => t.Comments)
                .HasForeignKey(c => c.ForumTopicId)
                .OnDelete(DeleteBehavior.Cascade);
            modelBuilder.Entity<Comment>()
                .HasOne(c => c.User)
                .WithMany()
                .HasForeignKey(c => c.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure EnergySolution
            modelBuilder.Entity<EnergySolution>()
                .HasKey(e => e.Id);

            // Configure Expert
            modelBuilder.Entity<Expert>()
                .HasKey(e => e.Id);
            modelBuilder.Entity<Expert>()
                .HasOne(e => e.User)  // Use navigation property
                .WithOne()
                .HasForeignKey<Expert>("UserId")  // Explicit shadow property name
                .OnDelete(DeleteBehavior.Cascade);

            // Configure Vendor
            modelBuilder.Entity<Vendor>()
                .HasKey(v => v.Id);
            modelBuilder.Entity<Vendor>()
                .HasOne(v => v.ApplicationUser) // Changed from User to ApplicationUser
                .WithMany() // Assuming an ApplicationUser can be associated with multiple Vendor profiles (if applicable, otherwise use WithOne)
                .HasForeignKey(v => v.UserId); // UserId is now a string

            // Configure other entities
            modelBuilder.Entity<EnergyProduct>()
                .HasKey(p => p.Id);
            modelBuilder.Entity<EnergyProduct>()
                .HasOne(p => p.Vendor)
                .WithMany() // Assuming a vendor can have multiple products; if not, adjust accordingly
                .HasForeignKey(p => p.VendorId)
                .OnDelete(DeleteBehavior.SetNull); // Or DeleteBehavior.Restrict, depending on desired behavior when a vendor is deleted

            modelBuilder.Entity<EducationalContent>()
                .HasKey(e => e.Id);

            modelBuilder.Entity<Testimonial>()
                .HasKey(t => t.Id);

            modelBuilder.Entity<Statistics>()
                .HasKey(s => s.Id);

            // Configure ExpertMessage
            modelBuilder.Entity<ExpertMessage>()
                .HasKey(m => m.Id);
            modelBuilder.Entity<ExpertMessage>()
                .HasOne(m => m.Expert)
                .WithMany()
                .HasForeignKey(m => m.ExpertId)
                .OnDelete(DeleteBehavior.Cascade);
            modelBuilder.Entity<ExpertMessage>()
                .HasOne(m => m.Sender)
                .WithMany()
                .HasForeignKey(m => m.SenderUserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure ForumTopic
            modelBuilder.Entity<ForumTopic>()
                .HasKey(f => f.Id);
            modelBuilder.Entity<ForumTopic>()
                .Property(f => f.Title)
                .IsRequired()
                .HasMaxLength(200);
            modelBuilder.Entity<ForumTopic>()
                .Property(f => f.Description)
                .IsRequired();
            modelBuilder.Entity<ForumTopic>()
                .Property(f => f.PostDate)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<SolutionFeedback>()
                .HasKey(f => f.Id);
            modelBuilder.Entity<SolutionFeedback>()
                .HasOne(f => f.User)
                .WithMany()
                .HasForeignKey(f => f.UserId)
                .OnDelete(DeleteBehavior.Restrict);
            modelBuilder.Entity<SolutionFeedback>()
                .HasOne(f => f.EnergySolution)
                .WithMany(s => s.Feedbacks)
                .HasForeignKey(f => f.EnergySolutionId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}