using Microsoft.EntityFrameworkCore;
using AgriEnergyPlatform.Data;
using AgriEnergyPlatform.Models;

namespace AgriEnergyPlatform.Services
{
    public class HomePageService : IHomePageService
    {
        private readonly ApplicationDbContext _context;

        public HomePageService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<HomePageData> GetHomePageData()
        {
            // Count farmers (users with role "Farmer")
            int farmers = await _context.Users.CountAsync(u => u.Role == "Farmer");
            // Count energy experts
            int experts = await _context.Experts.CountAsync();
            // Count projects/solutions
            int projects = await _context.EnergySolutions.CountAsync();
            // Calculate average cost savings (if available)
            decimal avgCostSavings = 0;
            // if (await _context.EnergySolutions.AnyAsync())
            // {
            //     var solutions = await _context.EnergySolutions.Include(es => es?.Feedbacks).ToListAsync();
            //     var feedbacks = solutions.SelectMany(s => s.Feedbacks).ToList();
            //     avgCostSavings = feedbacks.Any() ? feedbacks.Average(f => f.Rating) : 0;
            // }

            var stats = new Statistics
            {
                FarmersConnected = farmers,
                EnergyExperts = experts,
                ProjectsCompleted = projects,
                CostSavingsPercentage = (int)Math.Round(avgCostSavings)
            };

            return new HomePageData
            {
                Stats = stats,
                Testimonials = await _context.Testimonials.Take(3).ToListAsync(),
                Solutions = await _context.EnergySolutions.Take(4).ToListAsync()
            };
        }
    }
}