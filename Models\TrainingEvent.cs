using System;
using System.ComponentModel.DataAnnotations;

namespace AgriEnergyPlatform.Models
{
    public class TrainingEvent
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public string Time { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public bool IsOnline { get; set; }
        public bool IsFree { get; set; }
        public decimal Fee { get; set; }
        public string Link { get; set; } = string.Empty;
        public int? VenueId { get; set; }
        public Venue? Venue { get; set; }
        public string OrganizerId { get; set; } = string.Empty; // UserId of organizer
        public string OrganizerType { get; set; } = string.Empty; // "Expert" or "Vendor"
        public string? AdditionalInfo { get; set; }
    }
}