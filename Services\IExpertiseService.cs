using AgriEnergyPlatform.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AgriEnergyPlatform.Services
{
    public interface IExpertiseService
    {
        Task<List<Expert>> GetExpertsAsync(); // Add this method
        Task<Expert?> GetExpertByIdAsync(int id);
        Task AddExpertAsync(Expert expert);
        Task UpdateExpertAsync(Expert expert);
        Task DeleteExpertAsync(int id);
    }
}