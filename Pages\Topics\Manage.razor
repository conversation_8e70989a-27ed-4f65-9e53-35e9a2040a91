@page "/topics/manage" 
@using AgriEnergyPlatform.Services
@using AgriEnergyPlatform.Models
@using System.Security.Claims
@inject TopicService TopicService
@inject AuthenticationStateProvider AuthProvider

<h3>Manage Topics</h3>

@if (isAdmin)
{
    <button @onclick="ShowAddTopic">Add New Topic</button>
    
    @if (showAddTopic)
    {
        <EditForm Model="newTopic" OnValidSubmit="HandleAddTopic">
            <InputText @bind-Value="newTopic.Title" placeholder="Title" />
            <InputTextArea @bind-Value="newTopic.Description" placeholder="Description" />
            <button type="submit">Save</button>
        </EditForm>
    }
    
    @foreach (var topic in topics)
    {
        <div>
            <h4>@topic.Title</h4>
            <p>@topic.Description</p>
            <button @onclick="() => PublishTopic(topic.Id)" 
                    disabled="@topic.IsPublished">
                Publish
            </button>
        </div>
    }
}

@code {
    private List<ForumTopic> topics = new();
    private ForumTopic newTopic = new();
    private bool showAddTopic;
    private bool isAdmin;
    
    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthProvider.GetAuthenticationStateAsync();
        isAdmin = authState.User.IsInRole("Admin");
        
        if (isAdmin)
        {
            topics = await TopicService.GetUnpublishedTopicsAsync();
        }
    }
    
    private void ShowAddTopic() => showAddTopic = true;
    
    private async Task HandleAddTopic()
    {
        var authState = await AuthProvider.GetAuthenticationStateAsync();
        var userId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        
        if (userId != null)
        {
            await TopicService.AddTopicAsync(newTopic, userId);
            topics = await TopicService.GetUnpublishedTopicsAsync();
            newTopic = new ForumTopic();
            showAddTopic = false;
        }
    }
    
    private async Task PublishTopic(int topicId)
    {
        await TopicService.PublishTopicAsync(topicId);
        topics = await TopicService.GetUnpublishedTopicsAsync();
    }
}