using AgriEnergyPlatform.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AgriEnergyPlatform.Services
{
    public interface IUserService
    {
        Task<List<ApplicationUser>> GetAllUsersAsync();
        Task<ApplicationUser?> GetUserByIdAsync(string id);
        Task PromoteToExpertAsync(ApplicationUser user, string? bio, string? specialization, string? contactInfo);
    }
}
