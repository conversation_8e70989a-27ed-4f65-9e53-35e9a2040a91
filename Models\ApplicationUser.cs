using Microsoft.AspNetCore.Identity;

namespace AgriEnergyPlatform.Models
{
    public class ApplicationUser : IdentityUser
    {
        public string? Role { get; set; }
        public bool RequirePasswordChange { get; set; }
        public string? FullName { get; set; }
        public string? Name { get; set; } // Add this to match Register page requirements
        public string? CompanyName { get; set; }
        public string? ContactEmail { get; set; }
        public string? ContactPhoneNumber { get; set; }
    }
}