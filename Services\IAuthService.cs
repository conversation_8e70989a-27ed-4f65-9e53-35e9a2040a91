using AgriEnergyPlatform.Models;
using System.Security.Claims;

namespace AgriEnergyPlatform.Services
{
    public interface IAuthService
    {
        Task<(ApplicationUser? user, string? token)> Login(string email, string password);
        ClaimsPrincipal? ValidateJwtToken(string? token);
        Task<bool> Register(ApplicationUser user, string password);
        Task<bool> ChangePassword(string userId, string oldPassword, string newPassword);
        Task<bool> InitiatePasswordReset(string email);
        string HashPassword(string password);
        bool VerifyPassword(string password, string passwordHash);
    }
}