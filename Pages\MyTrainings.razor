@page "/my-trainings"
@using AgriEnergyPlatform.Models
@using AgriEnergyPlatform.Services
@inject ITrainingService TrainingService
@inject IVenueService VenueService
@inject AuthenticationStateProvider AuthProvider

<h2>My Registered Trainings</h2>

@* Aggregated Stats *@
@if (!isLoading && enrollments.Count > 0)
{
    var now = DateTime.Now;
    var total = enrollments.Count;
    var upcoming = enrollments.Count(e => trainings.FirstOrDefault(t => t.Id == e.TrainingId)?.Date >= now);
    var completed = enrollments.Count(e => trainings.FirstOrDefault(t => t.Id == e.TrainingId)?.Date < now);
    <div class="mb-4 d-flex gap-4">
        <div class="stat-card bg-light p-3 rounded"><strong>Total:</strong> @total</div>
        <div class="stat-card bg-light p-3 rounded"><strong>Upcoming:</strong> @upcoming</div>
        <div class="stat-card bg-light p-3 rounded"><strong>Completed:</strong> @completed</div>
    </div>
}

@if (isLoading)
{
    <div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>
}
else if (enrollments.Count == 0)
{
    <div class="alert alert-info">You have not registered for any trainings yet.</div>
}
else
{
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Title</th>
                <th>Date</th>
                <th>Type</th>
                <th>Venue</th>
                <th>Payment Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var entry in enrollments)
            {
                var training = trainings.FirstOrDefault(t => t.Id == entry.TrainingId);
                var venue = training?.VenueId != null ? venues.FirstOrDefault(v => v.Id == training.VenueId) : null;
                <tr>
                    <td>@training?.Title</td>
                    <td>@training?.Date.ToShortDateString()</td>
                    <td>@(training?.IsOnline == true ? "Online" : "Physical")</td>
                    <td>@venue?.Name</td>
                    <td>@entry.PaymentStatus</td>
                    <td>
                        <a class="btn btn-sm btn-outline-primary" href="/training/@training?.Id">Details</a>
                        @if (training?.IsOnline == true && entry.PaymentStatus == "Paid")
                        {
                            <a class="btn btn-sm btn-success ms-2" href="@training.Link" target="_blank">Join</a>
                        }
                        <button class="btn btn-sm btn-danger ms-2" @onclick="() => CancelEnrollment(entry.TrainingId)">Cancel Registration</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}

@code {
    private List<TrainingEnrollment> enrollments = new();
    private List<TrainingEvent> trainings = new();
    private List<Venue> venues = new();
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        isLoading = true;
        var authState = await AuthProvider.GetAuthenticationStateAsync();
        var userId = authState.User?.Identity?.IsAuthenticated == true ? authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value : null;
        if (userId != null)
        {
            enrollments = await TrainingService.GetUserEnrollmentsAsync(userId);
            trainings = await TrainingService.GetUpcomingEventsAsync();
            venues = await VenueService.GetVenuesAsync();
        }
        isLoading = false;
    }

    private async Task CancelEnrollment(int trainingId)
    {
        isLoading = true;
        var authState = await AuthProvider.GetAuthenticationStateAsync();
        var userId = authState.User?.Identity?.IsAuthenticated == true ? authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value : null;
        if (userId != null)
        {
            await TrainingService.CancelEnrollmentAsync(trainingId, userId);
            enrollments = await TrainingService.GetUserEnrollmentsAsync(userId);
        }
        isLoading = false;
        StateHasChanged();
    }
}
