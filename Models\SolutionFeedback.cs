namespace AgriEnergyPlatform.Models;
public class SolutionFeedback
{
    public int Id { get; set; }
    public int EnergySolutionId { get; set; }
    public string UserId { get; set; } = string.Empty; // Ensure non-null value
    public string Comment { get; set; } = string.Empty; // Ensure non-null value
    public int Rating { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public ApplicationUser User { get; set; } = new ApplicationUser(); // Ensure non-null value
    public EnergySolution EnergySolution { get; set; } = new EnergySolution(); // Ensure non-null value
}