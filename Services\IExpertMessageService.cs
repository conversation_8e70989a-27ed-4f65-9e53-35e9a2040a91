using AgriEnergyPlatform.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AgriEnergyPlatform.Services
{
    public interface IExpertMessageService
    {
        Task SendMessageAsync(int expertId, string senderUserId, string message);
        Task<List<ExpertMessage>> GetMessagesForExpertAsync(int expertId);
        Task<int> GetUnreadCountForExpertAsync(int expertId);
        Task MarkMessagesAsReadAsync(int expertId);
    }
}
