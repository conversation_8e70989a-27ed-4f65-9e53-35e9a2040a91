using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AgriEnergyPlatform.Models
{
    public class Expert
    {
        public int Id { get; set; }
        public string UserId { get; set; }
        public string? Name { get; set; }
        public string? Specialization { get; set; }
        public string? Bio { get; set; }
        public string? ContactInfo { get; set; }
        [ForeignKey("UserId")]
        public virtual ApplicationUser? User { get; set; }
    }
}