@page "/"
@using AgriEnergyPlatform.Services
@using AgriEnergyPlatform.Models
@inject IHomePageService HomePageService
@inject IExpertService ExpertService
@implements IDisposable

<PageTitle>Home - Agri-Energy Connect</PageTitle>

<style>
    .hero-gradient {
        background: linear-gradient(135deg, rgba(46,125,50,0.95) 0%, rgba(29,79,32,0.95) 100%);
    }
    .feature-card {
        transition: all 0.3s ease;
        border: none;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    }
    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    }
    .icon-circle {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
    }
    .bg-icon-green {
        background-color: rgba(46,125,50,0.1);
        color: #2e7d32;
    }
    .bg-icon-orange {
        background-color: rgba(255,152,0,0.1);
        color: #ff9800;
    }
    .bg-icon-blue {
        background-color: rgba(33,150,243,0.1);
        color: #2196f3;
    }
    .stat-card {
        background: rgba(255,255,255,0.1);
        border-radius: 12px;
        padding: 1.5rem;
        transition: all 0.3s ease;
    }
    .stat-card:hover {
        background: rgba(255,255,255,0.2);
        transform: scale(1.03);
    }
    .testimonial-card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    }
    .btn-energy {
        background-color: #ff9800;
        color: white;
        border: none;
    }
    .btn-energy:hover {
        background-color: #f57c00;
        color: white;
    }
    .animate-delay-1 {
        animation-delay: 0.3s;
    }
    .animate-delay-2 {
        animation-delay: 0.6s;
    }
</style>

@if (homeData == null)
{
    <div class="d-flex justify-content-center align-items-center" style="height: 80vh;">
        <div class="spinner-grow text-success" style="width: 3rem; height: 3rem;" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
}
else
{
    <!-- Hero Section -->
    <section class="hero-section hero-gradient text-white py-5" style="min-height: 90vh; display: flex; align-items: center;">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center">
                    <h1 class="display-3 fw-bold mb-4 animate__animated animate__fadeInDown">Bridging Agriculture with Renewable Energy</h1>
                    <p class="lead mb-5 fs-4 animate__animated animate__fadeIn animate__delay-1s">A collaborative platform connecting farmers with energy specialists to create sustainable farming solutions</p>
                    <div class="d-flex gap-3 justify-content-center animate__animated animate__fadeIn animate__delay-2s">
                        <a href="#features" class="btn btn-light btn-lg px-4 py-3 rounded-pill fw-bold">Explore Solutions</a>
                        <a href="#connect" class="btn btn-outline-light btn-lg px-4 py-3 rounded-pill fw-bold">Find Experts</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5 my-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold display-5 mb-3 animate__animated animate__fadeIn">How Agri-Energy Connect Works</h2>
                <p class="lead text-muted animate__animated animate__fadeIn animate-delay-1">Our platform makes collaboration simple and effective</p>
            </div>
            
            <div class="row g-4">
                <div class="col-md-4 animate__animated animate__fadeInUp">
                    <div class="feature-card card h-100">
                        <div class="card-body text-center p-4">
                            <div class="icon-circle bg-icon-green">
                                <i class="fas fa-search fa-2x"></i>
                            </div>
                            <h3 class="h4 mb-3">Discover Solutions</h3>
                            <p class="text-muted">Explore renewable energy options tailored for agricultural needs from solar irrigation to biogas systems.</p>
                            <a href="#" class="btn btn-outline-success mt-3 rounded-pill px-4">Learn More</a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 animate__animated animate__fadeInUp animate-delay-1">
                    <div class="feature-card card h-100">
                        <div class="card-body text-center p-4">
                            <div class="icon-circle bg-icon-orange">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                            <h3 class="h4 mb-3">Connect with Experts</h3>
                            <p class="text-muted">Find and collaborate with certified renewable energy specialists who understand agricultural challenges.</p>
                            <a href="#" class="btn btn-energy mt-3 rounded-pill px-4">Find Experts</a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 animate__animated animate__fadeInUp animate-delay-2">
                    <div class="feature-card card h-100">
                        <div class="card-body text-center p-4">
                            <div class="icon-circle bg-icon-blue">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                            <h3 class="h4 mb-3">Monitor & Optimize</h3>
                            <p class="text-muted">Track your energy usage, savings, and environmental impact with our analytics dashboard.</p>
                            <a href="#" class="btn btn-outline-primary mt-3 rounded-pill px-4">View Features</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Solutions Section -->
    <section class="py-5 my-5 bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 mb-5 mb-lg-0 animate__animated animate__fadeInLeft">
                    <h2 class="fw-bold display-5 mb-4">Sustainable Energy Solutions for Agriculture</h2>
                    <p class="lead mb-4">Our platform showcases the most effective renewable energy technologies adapted for farming needs.</p>
                    <div class="row g-4">
                        @foreach (var solution in homeData.Solutions)
                        {
                            <div class="col-md-6">
                                <div class="d-flex align-items-start">
                                    <div class="me-3 text-success">
                                        <i class="@solution.Icon fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5 class="mb-1">@solution.Title</h5>
                                        <p class="text-muted small mb-0">@solution.Description</p>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
                <div class="col-lg-6 animate__animated animate__fadeInRight">
                    <img src="https://images.unsplash.com/photo-1508514177221-188b1cf16e9d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1472&q=80" 
                         alt="Solar panels in farm" 
                         class="img-fluid rounded-3 shadow-lg" 
                         style="max-height: 500px; width: 100%; object-fit: cover;">
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->

    <!-- Training Promotion Section -->
    <section class="py-5 my-5 bg-success bg-opacity-10">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h2 class="fw-bold mb-3 text-success">Upcoming Trainings & Events</h2>
                    <p class="lead mb-4">Grow your skills with expert-led sessions and practical workshops. Stay ahead in sustainable agriculture and energy!</p>
                    <a href="/training" class="btn btn-success btn-lg px-4 rounded-pill fw-bold">View Trainings</a>
                </div>
                <div class="col-lg-4 text-center">
                    <img src="https://cdn-icons-png.flaticon.com/512/3135/3135715.png" alt="Training" class="img-fluid" style="max-height: 180px;" />
                </div>
            </div>
        </div>
    </section>
    <section class="py-5 my-5 hero-gradient text-white">
        <div class="container">
            <div class="row g-4 text-center">
                <div class="col-md-3">
                    <div class="stat-card h-100 animate__animated animate__fadeIn">
                        <h3 class="fw-bold display-4">@homeData.Stats.FarmersConnected+</h3>
                        <p class="mb-0">Farmers Connected</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card h-100 animate__animated animate__fadeIn animate-delay-1">
                        <h3 class="fw-bold display-4">@homeData.Stats.EnergyExperts+</h3>
                        <p class="mb-0">Energy Experts</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card h-100 animate__animated animate__fadeIn animate-delay-2">
                        <h3 class="fw-bold display-4">@homeData.Stats.ProjectsCompleted+</h3>
                        <p class="mb-0">Projects Completed</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card h-100 animate__animated animate__fadeIn animate-delay-3">
                        <h3 class="fw-bold display-4">@homeData.Stats.CostSavingsPercentage%</h3>
                        <p class="mb-0">Average Cost Savings</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-5 my-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold display-5 mb-3">What Our Users Say</h2>
                <p class="lead text-muted">Success stories from farmers and energy specialists</p>
            </div>
            
            <div class="row g-4">
                @foreach (var testimonial in homeData.Testimonials)
                {
                    <div class="col-md-4">
                        <div class="testimonial-card card h-100">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center mb-3">
                                    <img src="@testimonial.ImageUrl" class="rounded-circle me-3" width="60" height="60" alt="@testimonial.Name">
                                    <div>
                                        <h5 class="mb-0">@testimonial.Name</h5>
                                        <small class="text-muted">@testimonial.Role</small>
                                    </div>
                                </div>
                                <p class="mb-0">"@testimonial.Comment"</p>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section id="connect" class="py-5 my-5">
        <div class="container">
            <div class="rounded-4 p-5 text-white position-relative overflow-hidden" 
                 style="background: linear-gradient(135deg, rgba(46,125,50,0.95) 0%, rgba(29,79,32,0.95) 100%);">
                <div class="row align-items-center position-relative z-index-1">
                    <div class="col-lg-8 mb-4 mb-lg-0">
                        <h2 class="fw-bold display-5 mb-3">Ready to Transform Your Farm's Energy Future?</h2>
                        <p class="lead mb-4">Join hundreds of farmers and energy specialists creating sustainable agricultural solutions.</p>
                        <div class="d-flex flex-wrap gap-3">
                            <a href="/register" class="btn btn-light btn-lg px-4 py-3 rounded-pill fw-bold">Get Started Now</a>
                            <a href="/contact" class="btn btn-outline-light btn-lg px-4 py-3 rounded-pill fw-bold">Contact Our Team</a>
                        </div>
                    </div>
                    <div class="col-lg-4 text-lg-end">
                        <img src="https://cdn-icons-png.flaticon.com/512/1005/1005141.png" class="img-fluid" style="max-height: 200px;" alt="Renewable energy illustration">
                    </div>
                </div>
            </div>
        </div>
    </section>
}

@code {
    private HomePageData? homeData;
    private Timer? animationTimer;
    private bool animationsEnabled = false;

    protected override async Task OnInitializedAsync()
    {
        homeData = await HomePageService.GetHomePageData();
        
        // Delay animations slightly for better perceived performance
        animationTimer = new Timer(_ => 
        {
            animationsEnabled = true;
            InvokeAsync(StateHasChanged);
        }, null, 300, Timeout.Infinite);
    }

    public void Dispose()
    {
        animationTimer?.Dispose();
    }
}