
@page "/insights"
@using AgriEnergyPlatform.Models
@using AgriEnergyPlatform.Services
@inject NavigationManager NavigationManager
@inject IForumService ForumService
@inject AuthenticationStateProvider AuthenticationStateProvider

<div class="insights-page">
    <div class="container my-5">
        <div class="section-title">
            <h1>Community Insights</h1>
            <p class="text-muted">Learn and share knowledge with fellow agricultural enthusiasts</p>
        </div>

        <div class="row g-4">
            <div class="col-lg-8">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Latest Discussions</h4>
                    @if (isAuthenticated)
                    {
                        <button class="btn btn-primary" @onclick="@(() => NavigationManager.NavigateTo("/insights/new"))">
                            <i class="fas fa-plus-circle me-2"></i>New Topic
                        </button>
                    }
                </div>

                <div class="card shadow-sm mb-4">
                    <div class="list-group list-group-flush">
                        @foreach (var topic in topics)
                        {
                            <a href="/insights/@topic.Id" class="list-group-item list-group-item-action p-3">
                                <div class="d-flex w-100 justify-content-between align-items-center">
                                    <h5 class="mb-1 fw-bold">@topic.Title</h5>
                                    <small class="text-muted">@topic?.PostDate.ToString("MMM d, yyyy")</small>
                                </div>
                                <p class="mb-1 text-muted">@topic.Description</p>
                                <div class="d-flex align-items-center mt-2">
                                    <div class="avatar me-2">
                                        <img src="/images/user-placeholder.png" width="44" height="44" class="rounded-circle" alt="User">
                                    </div>
                                    <small>Posted by @topic.AuthorName</small>
                                </div>
                            </a>
                        }
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="sticky-sidebar">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-white">
                            <h5 class="mb-0">Popular Tags</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex flex-wrap gap-2">
                                <a href="#" class="badge bg-light text-dark text-decoration-none p-2">Solar Energy</a>
                                <a href="#" class="badge bg-light text-dark text-decoration-none p-2">Biogas</a>
                                <a href="#" class="badge bg-light text-dark text-decoration-none p-2">Wind Power</a>
                                <a href="#" class="badge bg-light text-dark text-decoration-none p-2">Sustainability</a>
                                <a href="#" class="badge bg-light text-dark text-decoration-none p-2">Farming</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0">Community Guidelines</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> Be respectful</li>
                                <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> Share facts</li>
                                <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> Cite sources</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> Stay on topic</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Add these styles to your existing CSS */
    .insights-page {
        min-height: calc(100vh - 200px); /* Adjust based on your header/footer height */
        padding-bottom: 2rem;
    }

    .sticky-sidebar {
        position: sticky;
        top: 1rem;
        max-height: calc(100vh - 2rem);
        overflow-y: auto;
    }

    .list-group-item {
        transition: var(--transition);
    }

    .list-group-item:hover {
        background-color: rgba(46, 125, 50, 0.05);
    }

    @@media (max-width: 992px) {
        .sticky-sidebar {
            position: static;
            max-height: none;
        }
    }
</style>

@code {
    private List<ForumTopic> topics = new();
    private bool isAuthenticated;

    protected override async Task OnInitializedAsync()
    {
        try 
        {
            topics = await ForumService.GetTopicsAsync();
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            isAuthenticated = authState.User.Identity?.IsAuthenticated ?? false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading insights: {ex.Message}");
        }
    }
}