@page "/training-register/{id:int}"
@using AgriEnergyPlatform.Models
@using AgriEnergyPlatform.Services

@inject ITrainingService TrainingService
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthProvider

@code {
    [Parameter]
    public int id { get; set; }
    private TrainingEvent? training;
    private bool isLoading = true;
    private string? errorMessage;
    private string? successMessage;
    private string? currentUserId;

    private async Task<string?> GetCurrentUserIdAsync()
    {
        var authState = await AuthProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        return user?.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
    }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            currentUserId = await GetCurrentUserIdAsync();
            Console.WriteLine($"Current user ID: {currentUserId}");
            training = (await TrainingService.GetUpcomingEventsAsync())?.FirstOrDefault(e => e.Id == id);
            if (training == null)
            {
                errorMessage = "Training event not found.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading training: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task Register()
    {
        currentUserId = await GetCurrentUserIdAsync();
        if (currentUserId == null || training == null) return;
        isLoading = true;
        Console.WriteLine($"Registering user {currentUserId} for training {training.Id}");
        try
        {
            await TrainingService.EnrollInTrainingAsync(training.Id, currentUserId);
            successMessage = "Successfully registered for the event!";
        }
        catch (Exception ex)
        {
            errorMessage = $"Registration failed: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
}

@if (isLoading)
{
    <div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>
}
else if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger">@errorMessage</div>
}
else if (training != null)
{
    <div class="card mt-5">
        <div class="card-header bg-primary text-white">
            <h4>@training.Title</h4>
        </div>
        <div class="card-body">
            <p><strong>Date:</strong> @training.Date.ToString("dd MMM yyyy")</p>
            <p><strong>Time:</strong> @training.Time</p>
            <p><strong>Description:</strong> @training.Description</p>
            <p><strong>Location:</strong> @(training.IsOnline ? "Online" : training.Location)</p>
            <p><strong>Fee:</strong> @(training.IsFree ? "FREE" : $"${training.Fee}")</p>
            @if (!string.IsNullOrEmpty(successMessage))
            {
                <div class="alert alert-success">@successMessage</div>
            }
            else
            {
                <button class="btn btn-success" @onclick="Register">Register Now</button>
            }
        </div>
    </div>
}
