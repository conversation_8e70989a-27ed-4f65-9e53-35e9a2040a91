using AgriEnergyPlatform.Data;
using AgriEnergyPlatform.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AgriEnergyPlatform.Services
{
    public class ExpertiseService : IExpertiseService
    {
        private readonly ApplicationDbContext _context;

        public ExpertiseService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<Expert>> GetExpertsAsync()
        {
            return await _context.Experts.ToListAsync();
        }

        public async Task<Expert?> GetExpertByIdAsync(int id)
        {
            return await _context.Experts.FindAsync(id);
        }

        public async Task AddExpertAsync(Expert expert)
        {
            _context.Experts.Add(expert);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateExpertAsync(Expert expert)
        {
            _context.Experts.Update(expert);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteExpertAsync(int id)
        {
            var expert = await _context.Experts.FindAsync(id);
            if (expert != null)
            {
                _context.Experts.Remove(expert);
                await _context.SaveChangesAsync();
            }
        }
    }
}