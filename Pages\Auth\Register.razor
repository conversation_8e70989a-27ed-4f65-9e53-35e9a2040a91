@page "/register"
@using AgriEnergyPlatform.Services
@using AgriEnergyPlatform.Models
@using System.ComponentModel.DataAnnotations
@inject IAuthService AuthService
@inject NavigationManager NavigationManager

<br/>
<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center align-items-center" style="min-height: 100vh;">
            <div class="col-lg-10">
                <div class="card auth-card shadow-lg">
                    <div class="row g-0">
                        <!-- Left side with image -->
                        <div class="col-md-6 d-none d-md-block">
                            <div class="auth-image h-100">
                                <img src="images/5.jpg" 
                                     alt="Sustainable farming" 
                                     class="img-fluid h-100 w-100 object-fit-cover rounded-start">
                                <div class="image-overlay p-4">
                                    <h2 class="text-white">Join Our Community</h2>
                                    <p class="text-white-50">Become part of the sustainable agriculture movement</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Right side with registration form -->
                        <div class="col-md-6">
                            <div class="card-body p-4 p-md-5">
                                <div class="text-center mb-4">
                                    <i class="fas fa-leaf text-primary mb-3" style="font-size: 2.5rem;"></i>
                                    <h2>Create Account</h2>
                                    <p class="text-muted">Fill in your details to get started</p>
                                </div>
                                
                                @if (!string.IsNullOrEmpty(error))
                                {
                                    <div class="alert alert-danger d-flex align-items-center">
                                        <i class="fas fa-exclamation-circle me-2"></i>
                                        <div>@error</div>
                                    </div>
                                }

                                <EditForm Model="registerModel" OnValidSubmit="HandleRegister">
                                    <DataAnnotationsValidator />

                                    <div class="mb-4">
                                        <label class="form-label">Full Name</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light">
                                                <i class="fas fa-user text-muted"></i>
                                            </span>
                                            <InputText @bind-Value="registerModel.Name" class="form-control" placeholder="Enter your full name" />
                                        </div>
                                        <ValidationMessage For="@(() => registerModel.Name)" class="text-danger small" />
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Email Address</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light">
                                                <i class="fas fa-envelope text-muted"></i>
                                            </span>
                                            <InputText @bind-Value="registerModel.Email" class="form-control" placeholder="Enter your email" />
                                        </div>
                                        <ValidationMessage For="@(() => registerModel.Email)" class="text-danger small" />
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Account Type</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light">
                                                <i class="fas fa-user-tag text-muted"></i>
                                            </span>
                                            <InputSelect @bind-Value="registerModel.Role" class="form-select">
                                                <option value="User">Regular User</option>
                                                <option value="Farmer">Farmer</option>
                                                <option value="Expert">Energy Expert</option>
                                                <option value="Vendor">Vendor</option>
                                            </InputSelect>
                                        </div>
                                    </div>

                                    @if (registerModel.Role == "Vendor")
                                    {
                                        <div class="mb-4">
                                            <label class="form-label">Company Name</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-light">
                                                    <i class="fas fa-building text-muted"></i>
                                                </span>
                                                <InputText @bind-Value="registerModel.CompanyName" class="form-control" placeholder="Enter your company name" />
                                            </div>
                                            <ValidationMessage For="@(() => registerModel.CompanyName)" class="text-danger small" />
                                        </div>

                                        <div class="mb-4">
                                            <label class="form-label">Contact Email (for Vendor)</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-light">
                                                    <i class="fas fa-envelope-open-text text-muted"></i>
                                                </span>
                                                <InputText @bind-Value="registerModel.ContactEmail" class="form-control" placeholder="Enter vendor contact email" />
                                            </div>
                                            <ValidationMessage For="@(() => registerModel.ContactEmail)" class="text-danger small" />
                                        </div>

                                        <div class="mb-4">
                                            <label class="form-label">Contact Phone (for Vendor)</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-light">
                                                    <i class="fas fa-phone-alt text-muted"></i>
                                                </span>
                                                <InputText @bind-Value="registerModel.ContactPhone" class="form-control" placeholder="Enter vendor contact phone" />
                                            </div>
                                            <ValidationMessage For="@(() => registerModel.ContactPhone)" class="text-danger small" />
                                        </div>
                                    }

                                    <div class="mb-4">
                                        <label class="form-label">Password</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light">
                                                <i class="fas fa-lock text-muted"></i>
                                            </span>
                                            <InputText type="password" @bind-Value="registerModel.Password" class="form-control" placeholder="Create a password" />
                                        </div>
                                        <ValidationMessage For="@(() => registerModel.Password)" class="text-danger small" />
                                        <div class="form-text">Must be at least 6 characters</div>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Confirm Password</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light">
                                                <i class="fas fa-lock text-muted"></i>
                                            </span>
                                            <InputText type="password" @bind-Value="registerModel.ConfirmPassword" class="form-control" placeholder="Confirm your password" />
                                        </div>
                                        <ValidationMessage For="@(() => registerModel.ConfirmPassword)" class="text-danger small" />
                                    </div>

                                    <div class="form-check mb-4">
                                        <input class="form-check-input" type="checkbox" id="termsCheck">
                                        <label class="form-check-label" for="termsCheck">
                                            I agree to the <a href="#" class="text-decoration-none">Terms</a> and <a href="#" class="text-decoration-none">Privacy Policy</a>
                                        </label>
                                    </div>

                                    <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
                                        <i class="fas fa-user-plus me-2"></i> Register
                                    </button>
                                    
                                    <div class="text-center mt-4">
                                        <p class="text-muted">Already have an account? 
                                            <a href="/login" class="text-decoration-none fw-bold">Sign in</a>
                                        </p>
                                    </div>
                                </EditForm>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<br/>
<style>
    .auth-card {
        border: none;
        border-radius: 1rem;
        overflow: hidden;
    }
    
    .auth-image {
        position: relative;
    }
    
    .image-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0,0,0,0.7));
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #2e7d32;
        box-shadow: 0 0 0 0.25rem rgba(46, 125, 50, 0.25);
    }
    
    .btn-primary {
        background-color: #2e7d32;
        border-color: #2e7d32;
    }
    
    .form-check-input:checked {
        background-color: #2e7d32;
        border-color: #2e7d32;
    }
</style>

@code {
    private ApplicationUser newUser = new();
    private RegisterModel registerModel = new();
    private string error = string.Empty;

    private async Task HandleRegister()
    {
        if (registerModel.Password != registerModel.ConfirmPassword)
        {
            error = "Passwords do not match";
            return;
        }

        var user = new ApplicationUser
        {
            UserName = registerModel.Name,
            Email = registerModel.Email,
            Name = registerModel.Name,
            Role = registerModel.Role,
            // Pass vendor-specific info if role is Vendor
            // These will be handled by AuthService to create/update Vendor entity
            CompanyName = registerModel.Role == "Vendor" ? registerModel.CompanyName : null,
            ContactEmail = registerModel.Role == "Vendor" ? registerModel.ContactEmail : null,
            ContactPhoneNumber = registerModel.Role == "Vendor" ? registerModel.ContactPhone : null
        };

        var result = await AuthService.Register(user, registerModel.Password);
        if (!result)
        {
            error = "Registration failed. Email might already be in use.";
            return;
        }

        NavigationManager.NavigateTo("/login");
    }

    public class RegisterModel
    {
        [Required(ErrorMessage = "Full name is required")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string Email { get; set; } = string.Empty;

        [Required]
        public string Role { get; set; } = "User";

        [Required(ErrorMessage = "Password is required")]
        [MinLength(6, ErrorMessage = "Password must be at least 6 characters")]
        public string Password { get; set; } = string.Empty;

        [Required(ErrorMessage = "Please confirm your password")]
        [Compare(nameof(Password), ErrorMessage = "Passwords do not match")]
        public string ConfirmPassword { get; set; } = string.Empty;

        // Vendor specific fields
        public string? CompanyName { get; set; } 
        [EmailAddress(ErrorMessage = "Invalid email format for contact email.")]
        public string? ContactEmail { get; set; }
        [Phone(ErrorMessage = "Invalid phone number format for contact phone.")]
        public string? ContactPhone { get; set; }
    }
}