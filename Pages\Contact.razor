@page "/contact"
@using AgriEnergyPlatform.Services
@inject GmailEmailService GmailEmailService

<EditForm Model="@emailModel" OnValidSubmit="SendEmail">
    <InputText @bind-Value="emailModel.To" placeholder="Recipient Email" class="form-control mb-2" />
    <InputText @bind-Value="emailModel.Subject" placeholder="Subject" class="form-control mb-2" />
    <InputTextArea @bind-Value="emailModel.Body" placeholder="Message" class="form-control mb-2" />
    <button type="submit" class="btn btn-primary">Send</button>
</EditForm>

@if (!string.IsNullOrEmpty(statusMessage))
{
    <div class="mt-2">@statusMessage</div>
}

@code {
    private EmailModel emailModel = new();
    private string statusMessage;

    private async Task SendEmail()
    {
        try
        {
            await GmailEmailService.SendEmailAsync(emailModel.To, emailModel.Subject, emailModel.Body);
            statusMessage = "Email sent successfully!";
        }
        catch (Exception ex)
        {
            statusMessage = $"Error sending email: {ex.Message}";
        }
    }

    public class EmailModel
    {
        public string To { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
    }
}
