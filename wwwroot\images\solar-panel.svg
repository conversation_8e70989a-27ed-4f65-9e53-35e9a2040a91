<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="solar-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4285F4;stop-opacity:0.2"/>
      <stop offset="100%" style="stop-color:#4285F4;stop-opacity:0.1"/>
    </linearGradient>
  </defs>
  <g fill="none" fill-rule="evenodd">
    <rect x="40" y="40" width="120" height="120" rx="8" fill="url(#solar-gradient)"/>
    <g transform="translate(60,60)">
      <rect x="0" y="0" width="30" height="30" rx="2" fill="#4285F4" opacity="0.8"/>
      <rect x="40" y="0" width="30" height="30" rx="2" fill="#4285F4" opacity="0.8"/>
      <rect x="0" y="40" width="30" height="30" rx="2" fill="#4285F4" opacity="0.8"/>
      <rect x="40" y="40" width="30" height="30" rx="2" fill="#4285F4" opacity="0.8"/>
    </g>
  </g>
</svg>