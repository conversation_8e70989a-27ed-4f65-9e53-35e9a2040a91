using AgriEnergyPlatform.Models;
using AgriEnergyPlatform.Data;
using Microsoft.EntityFrameworkCore;

namespace AgriEnergyPlatform.Services
{
    public class ForumService : IForumService
    {
        private readonly ApplicationDbContext _context;

        public ForumService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<ForumTopic>> GetTopicsAsync()
        {
            return await _context.ForumTopics.ToListAsync();
        }

        public async Task<ForumTopic?> GetTopicByIdAsync(int id)
        {
            return await _context.ForumTopics.FindAsync(id);
        }

        public async Task AddTopicAsync(ForumTopic topic, string userId)
        {
            topic.UserId = userId;
            topic.CreatedAt = DateTime.UtcNow;
            
            _context.ForumTopics.Add(topic);
            await _context.SaveChangesAsync();
        }
    }
}
