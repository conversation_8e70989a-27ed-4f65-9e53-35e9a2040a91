@page "/training/{Id:int}"
@using AgriEnergyPlatform.Models
@using AgriEnergyPlatform.Services

@inject NavigationManager Navigation
@inject IVenueService VenueService
@inject ITrainingService TrainingService
@inject AuthenticationStateProvider AuthProvider

@code {
    [Parameter]
    public int Id { get; set; }
    private TrainingEvent? training;
    private Venue? venue;
    private string? organizerName;
    private bool isRegistered = false;
    private string? paymentStatus;
    private bool isLoading = true;
    private string? errorMessage;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            isLoading = true;
            training = (await TrainingService.GetUpcomingEventsAsync()).FirstOrDefault(t => t.Id == Id);
            if (training == null)
            {
                errorMessage = "Training not found.";
                return;
            }
            if (training.VenueId.HasValue)
            {
                venue = await VenueService.GetVenueByIdAsync(training.VenueId.Value);
            }
            // Organizer info (simplified for demo)
            organizerName = training.OrganizerType + ": " + training.OrganizerId;
            // Registration/payment status for current user
            var authState = await AuthProvider.GetAuthenticationStateAsync();
            var userId = authState.User?.Identity?.IsAuthenticated == true ? authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value : null;
            if (userId != null)
            {
                var enrollments = await TrainingService.GetUserEnrollmentsAsync(userId);
                var myEnroll = enrollments.FirstOrDefault(e => e.TrainingId == Id);
                if (myEnroll != null)
                {
                    isRegistered = true;
                    paymentStatus = myEnroll.PaymentStatus;
                }
            }
        }
        finally
        {
            isLoading = false;
        }
    }
}

@if (isLoading)
{
    <div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>
}
else if (errorMessage != null)
{
    <div class="alert alert-danger">@errorMessage</div>
}
else if (training != null)
{
    <div class="card mb-4">
        <div class="card-body">
            <h3>@training.Title</h3>
            <p>@training.Description</p>
            <ul class="list-unstyled">
                <li><b>Date:</b> @training.Date.ToShortDateString() @training.Time</li>
                <li><b>Type:</b> @(training.IsOnline ? "Online" : "Physical")</li>
                <li><b>Fee:</b> @(training.IsFree ? "Free" : ($"{training.Fee:C}"))</li>
                <li><b>Organizer:</b> @organizerName</li>
                @if (venue != null)
                {
                    <li><b>Venue:</b> @venue.Name, @venue.Address</li>
                }
                <li><b>Additional Info:</b> @training.AdditionalInfo</li>
            </ul>
            @if (isRegistered)
            {
                <div class="alert alert-success">You are registered for this training. Payment Status: @paymentStatus</div>
                @if (training.IsOnline && paymentStatus == "Paid")
                {
                    <a class="btn btn-primary" href="@training.Link" target="_blank" rel="noopener noreferrer">Join Online Event</a>
                }
            }
            else
            {
                <button class="btn btn-success" @onclick="NavigateToRegister">Register</button>
            }
        </div>
    </div>
}


@code {
    private void NavigateToRegister()
    {
        Navigation.NavigateTo($"/training-register/{Id}");
    }
}